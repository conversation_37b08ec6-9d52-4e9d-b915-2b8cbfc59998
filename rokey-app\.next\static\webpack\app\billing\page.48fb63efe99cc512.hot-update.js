"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        interval: 'forever',\n        features: [\n            {\n                name: 'Strict fallback routing only',\n                included: true\n            },\n            {\n                name: 'Basic analytics',\n                included: true\n            },\n            {\n                name: 'No custom roles',\n                included: false\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '1 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '3 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '3 max'\n            }\n        ]\n    },\n    {\n        id: 'starter',\n        name: 'Starter',\n        price: 19,\n        interval: 'month',\n        popular: true,\n        features: [\n            {\n                name: 'All routing strategies',\n                included: true\n            },\n            {\n                name: 'Advanced analytics',\n                included: true\n            },\n            {\n                name: 'Custom roles',\n                included: true,\n                limit: 'Up to 3 roles'\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '5 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '15 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '50 max'\n            },\n            {\n                name: 'Browsing tasks',\n                included: true,\n                limit: '15/month'\n            }\n        ]\n    },\n    {\n        id: 'professional',\n        name: 'Professional',\n        price: 49,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Starter',\n                included: true\n            },\n            {\n                name: 'Unlimited configurations',\n                included: true\n            },\n            {\n                name: 'Unlimited API keys per config',\n                included: true\n            },\n            {\n                name: 'Unlimited user-generated API keys',\n                included: true\n            },\n            {\n                name: 'Knowledge base documents',\n                included: true,\n                limit: '5 documents'\n            },\n            {\n                name: 'Priority support',\n                included: true\n            }\n        ]\n    },\n    {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 149,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Professional',\n                included: true\n            },\n            {\n                name: 'Unlimited knowledge base documents',\n                included: true\n            },\n            {\n                name: 'Advanced semantic caching',\n                included: true\n            },\n            {\n                name: 'Custom integrations',\n                included: true\n            },\n            {\n                name: 'Dedicated support + phone',\n                included: true\n            },\n            {\n                name: 'SLA guarantee',\n                included: true\n            }\n        ]\n    }\n];\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, subscriptionStatus, refreshSubscription, createCheckoutSession } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal (mock data for now)\n    const daysUntilRenewal = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' ? Math.floor(Math.random() * 30) + 1 : null;\n    const handlePlanChange = async (planId)=>{\n        if (planId === currentPlan.id) return;\n        const targetPlan = plans.find((p)=>p.id === planId);\n        if (!targetPlan) return;\n        const isUpgrade = plans.findIndex((p)=>p.id === planId) > plans.findIndex((p)=>p.id === currentPlan.id);\n        if (isUpgrade) {\n            // For upgrades, go directly to Stripe checkout\n            try {\n                setLoading(true);\n                await createCheckoutSession(planId);\n            } catch (error) {\n                console.error('Checkout error:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to start checkout. Please try again.');\n                setLoading(false);\n            }\n        } else {\n            // For downgrades, show confirmation\n            confirmation.showConfirmation({\n                title: 'Downgrade Plan',\n                message: \"Are you sure you want to downgrade to the \".concat(targetPlan.name, \" plan? This will take effect at the end of your current billing period.\"),\n                confirmText: 'Downgrade',\n                cancelText: 'Cancel',\n                type: 'warning'\n            }, async ()=>{\n                setLoading(true);\n                try {\n                    // TODO: Implement downgrade logic with Stripe\n                    // For now, we'll simulate the process\n                    await new Promise((resolve)=>setTimeout(resolve, 2000));\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Successfully scheduled downgrade to \".concat(targetPlan.name, \" plan\"));\n                    await refreshSubscription();\n                } catch (error) {\n                    console.error('Downgrade error:', error);\n                    sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to downgrade plan. Please try again.');\n                } finally{\n                    setLoading(false);\n                }\n            });\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Billing & Plans\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Manage your subscription, billing information, and plan features.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Current Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: currentPlan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-orange-100 text-orange-800\",\n                                                                children: \"Popular\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mt-1\",\n                                                        children: currentPlan.price === 0 ? 'Free forever' : \"$\".concat(currentPlan.price, \"/\").concat(currentPlan.interval)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mt-2 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Renews in \",\n                                                                    daysUntilRenewal,\n                                                                    \" days\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setShowCancelModal(true),\n                                                    className: \"text-red-600 border-red-200 hover:bg-red-50\",\n                                                    children: \"Cancel Subscription\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                    children: \"Available Plans\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                    children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"relative \".concat(plan.popular ? 'ring-2 ring-orange-500' : ''),\n                                            children: [\n                                                plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"bg-orange-500 text-white\",\n                                                        children: \"Most Popular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold\",\n                                                                children: plan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            plan.price\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: [\n                                                                            \"/\",\n                                                                            plan.interval\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 44\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-3 mb-6\",\n                                                            children: plan.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-green-500 flex-shrink-0 mt-0.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                            lineNumber: 295,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-300 flex-shrink-0 mt-0.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm \".concat(feature.included ? 'text-gray-900' : 'text-gray-400'),\n                                                                            children: [\n                                                                                feature.name,\n                                                                                feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: [\n                                                                                        \" (\",\n                                                                                        feature.limit,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                                    lineNumber: 302,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                            lineNumber: 299,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 293,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        plan.id === currentPlan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            disabled: true,\n                                                            className: \"w-full\",\n                                                            children: \"Current Plan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: ()=>handlePlanChange(plan.id),\n                                                            disabled: loading,\n                                                            className: \"w-full\",\n                                                            variant: plan.popular ? \"default\" : \"outline\",\n                                                            children: plans.findIndex((p)=>p.id === plan.id) > plans.findIndex((p)=>p.id === currentPlan.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Upgrade to \",\n                                                                    plan.name\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Downgrade to \",\n                                                                    plan.name\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, plan.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this),\n                showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Cancel Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Reason for cancellation *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: cancelReason,\n                                                onChange: (e)=>setCancelReason(e.target.value),\n                                                className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a reason...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: reason,\n                                                            children: reason\n                                                        }, reason, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Additional feedback (optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: cancelFeedback,\n                                                onChange: (e)=>setCancelFeedback(e.target.value),\n                                                placeholder: \"Tell us more about your experience or what we could do better...\",\n                                                rows: 3,\n                                                className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowCancelModal(false),\n                                        className: \"flex-1\",\n                                        children: \"Keep Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleCancelSubscription,\n                                        disabled: loading || !cancelReason.trim(),\n                                        className: \"flex-1 bg-red-600 hover:bg-red-700\",\n                                        children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 384,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"h0GVZ6vMBL+KnMClHQ33C7lijVs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ })

});