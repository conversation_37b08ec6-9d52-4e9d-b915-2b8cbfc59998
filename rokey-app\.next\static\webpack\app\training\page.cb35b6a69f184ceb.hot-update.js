"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded, onDocumentDeleted } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation)();\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Replace with server data to avoid duplicates\n                    setDocuments(newDocuments.sort({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            } finally{\n                if (retryCount === 0 || expectedDocumentId) {\n                    setIsRefreshing(false);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename || file.name,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status || 'processing',\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>{\n                // Check if document already exists (shouldn't happen, but safety check)\n                const exists = prev.find((doc)=>doc.id === optimisticDocument.id);\n                if (exists) {\n                    console.log(\"[DocumentUpload] Document \".concat(optimisticDocument.id, \" already exists, updating instead\"));\n                    return prev.map((doc)=>doc.id === optimisticDocument.id ? optimisticDocument : doc);\n                }\n                return [\n                    optimisticDocument,\n                    ...prev\n                ];\n            });\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = (documentId, documentName)=>{\n        confirmation.showConfirmation({\n            title: 'Delete Document',\n            message: 'Are you sure you want to delete \"'.concat(documentName, '\"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.'),\n            confirmText: 'Delete Document',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            // Optimistically remove the document from the list\n            const originalDocuments = documents;\n            setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n            try {\n                const response = await fetch(\"/api/documents/\".concat(documentId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    // Restore the original list on error\n                    setDocuments(originalDocuments);\n                    throw new Error('Failed to delete document');\n                }\n                setSuccess('Document deleted successfully');\n                // Call callback if provided\n                onDocumentDeleted === null || onDocumentDeleted === void 0 ? void 0 : onDocumentDeleted();\n                // Auto-clear success message after 3 seconds\n                setTimeout(()=>setSuccess(null), 3000);\n            } catch (err) {\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                const errorMessage = \"Delete failed: \".concat(err.message);\n                setError(errorMessage);\n                // Auto-clear error message after 8 seconds\n                setTimeout(()=>setError(null), 8000);\n            }\n        });\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 286,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 287,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 288,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800 text-sm font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 304,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Uploaded Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Refreshing...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 369,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id, doc.filename),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 368,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 431,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"qy7mnAkmgJ7Ofmiub+gVBhPvEic=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation\n    ];\n});\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});