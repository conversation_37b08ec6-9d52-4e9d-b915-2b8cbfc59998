"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction TrainingPage() {\n    _s();\n    // Subscription hook\n    const { subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation)();\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentCount, setDocumentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Fetch document count for a configuration\n    const fetchDocumentCount = async function(configId) {\n        let retryCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        try {\n            // Add cache-busting parameter to ensure fresh data\n            const timestamp = Date.now();\n            const response = await fetch(\"/api/documents/list?configId=\".concat(configId, \"&_t=\").concat(timestamp), {\n                cache: 'no-store' // Ensure we don't get cached responses\n            });\n            if (response.ok) {\n                var _data_documents;\n                const data = await response.json();\n                const newCount = ((_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.length) || 0;\n                console.log(\"[Training Page] Document count updated: \".concat(newCount, \" for config: \").concat(configId));\n                setDocumentCount(newCount);\n            } else {\n                console.error('Error fetching document count:', response.status, response.statusText);\n                // Retry once if the request failed\n                if (retryCount < 1) {\n                    setTimeout(()=>fetchDocumentCount(configId, retryCount + 1), 1000);\n                }\n            }\n        } catch (err) {\n            console.error('Error fetching document count:', err);\n            // Retry once if there was an error\n            if (retryCount < 1) {\n                setTimeout(()=>fetchDocumentCount(configId, retryCount + 1), 1000);\n            }\n        }\n    };\n    // Fetch document count when config changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                fetchDocumentCount(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900\",\n                                    children: \"AI Training & Enhancement\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                subscriptionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierBadge, {\n                                    tier: subscriptionStatus.tier,\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl\",\n                            children: \"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-red-50 border border-red-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm font-medium\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-green-50 border border-green-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm font-medium\",\n                                children: successMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"knowledge_base\",\n                    customMessage: \"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-orange-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Knowledge Documents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            subscriptionStatus && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.LimitIndicator, {\n                                    current: documentCount,\n                                    limit: subscriptionStatus.tier === 'professional' ? 5 : subscriptionStatus.tier === 'enterprise' ? 999999 : 0,\n                                    label: \"Knowledge Base Documents\",\n                                    tier: subscriptionStatus.tier,\n                                    showUpgradeHint: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                configId: selectedConfigId,\n                                onDocumentUploaded: ()=>{\n                                    console.log('Document uploaded successfully');\n                                    // Add a small delay to ensure database consistency before refreshing count\n                                    if (selectedConfigId) {\n                                        setTimeout(()=>{\n                                            fetchDocumentCount(selectedConfigId);\n                                        }, 500); // 500ms delay\n                                    }\n                                },\n                                onDocumentDeleted: ()=>{\n                                    console.log('Document deleted successfully');\n                                    // Add a small delay to ensure database consistency before refreshing count\n                                    if (selectedConfigId) {\n                                        setTimeout(()=>{\n                                            fetchDocumentCount(selectedConfigId);\n                                        }, 500); // 500ms delay\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"prompt_engineering\",\n                    customMessage: \"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Define behavior, examples, and instructions for your AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"configSelect\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Select API Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"configSelect\",\n                                                value: selectedConfigId,\n                                                onChange: (e)=>setSelectedConfigId(e.target.value),\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Choose which model to train...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: config.id,\n                                                            children: config.name\n                                                        }, config.id, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"trainingPrompts\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Custom Prompts & Instructions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"trainingPrompts\",\n                                                value: trainingPrompts,\n                                                onChange: (e)=>setTrainingPrompts(e.target.value),\n                                                placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                                rows: 12,\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                        children: \"Training Format Guide:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-xs text-blue-800 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"SYSTEM:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Core instructions for the AI's role and personality\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"BEHAVIOR:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Guidelines for how the AI should behave\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Examples:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    ' Use \"User input → Expected response\" format'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"General:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Any other instructions written as normal text\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-secondary\",\n                                                    onClick: ()=>{\n                                                        confirmation.showConfirmation({\n                                                            title: 'Clear Training Prompts',\n                                                            message: 'Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.',\n                                                            confirmText: 'Clear All',\n                                                            cancelText: 'Cancel',\n                                                            type: 'warning'\n                                                        }, ()=>{\n                                                            setTrainingPrompts('');\n                                                        });\n                                                    },\n                                                    children: \"Clear Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleStartTraining,\n                                                disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                                className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Processing Prompts...\"\n                                                    ]\n                                                }, void 0, true) : 'Save Prompts'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 337,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n            lineNumber: 243,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"UrnJmCfw8iCy300JBOY9o5vKe/8=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation\n    ];\n});\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});