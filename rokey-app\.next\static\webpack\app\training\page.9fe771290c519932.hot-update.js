"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TrainingPage() {\n    _s();\n    // Subscription hook\n    const { subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription)();\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentCount, setDocumentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Fetch document count for a configuration\n    const fetchDocumentCount = async (configId)=>{\n        try {\n            const response = await fetch(\"/api/documents?config_id=\".concat(configId));\n            if (response.ok) {\n                var _data_documents;\n                const data = await response.json();\n                setDocumentCount(((_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.length) || 0);\n            }\n        } catch (err) {\n            console.error('Error fetching document count:', err);\n        }\n    };\n    // Fetch document count when config changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                fetchDocumentCount(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900\",\n                                    children: \"AI Training & Enhancement\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                subscriptionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierBadge, {\n                                    tier: subscriptionStatus.tier,\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl\",\n                            children: \"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 228,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-red-50 border border-red-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm font-medium\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-green-50 border border-green-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm font-medium\",\n                                children: successMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"knowledge_base\",\n                    customMessage: \"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-orange-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Knowledge Documents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            subscriptionStatus && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.LimitIndicator, {\n                                    current: documentCount,\n                                    limit: subscriptionStatus.tier === 'professional' ? 5 : subscriptionStatus.tier === 'enterprise' ? 999999 : 0,\n                                    label: \"Knowledge Base Documents\",\n                                    tier: subscriptionStatus.tier,\n                                    showUpgradeHint: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                configId: selectedConfigId,\n                                onDocumentUploaded: ()=>{\n                                    // Optionally refresh something or show a message\n                                    console.log('Document uploaded successfully');\n                                    // Refresh document count\n                                    if (selectedConfigId) {\n                                        fetchDocumentCount(selectedConfigId);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"prompt_engineering\",\n                    customMessage: \"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Define behavior, examples, and instructions for your AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"configSelect\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Select API Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"configSelect\",\n                                                value: selectedConfigId,\n                                                onChange: (e)=>setSelectedConfigId(e.target.value),\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Choose which model to train...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: config.id,\n                                                            children: config.name\n                                                        }, config.id, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"trainingPrompts\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Custom Prompts & Instructions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"trainingPrompts\",\n                                                value: trainingPrompts,\n                                                onChange: (e)=>setTrainingPrompts(e.target.value),\n                                                placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                                rows: 12,\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                        children: \"Training Format Guide:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-xs text-blue-800 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"SYSTEM:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 372,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Core instructions for the AI's role and personality\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"BEHAVIOR:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Guidelines for how the AI should behave\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Examples:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    ' Use \"User input → Expected response\" format'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"General:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Any other instructions written as normal text\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-secondary\",\n                                                    onClick: ()=>{\n                                                        if (confirm('Clear all training prompts?')) {\n                                                            setTrainingPrompts('');\n                                                        }\n                                                    },\n                                                    children: \"Clear Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleStartTraining,\n                                                disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                                className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Processing Prompts...\"\n                                                    ]\n                                                }, void 0, true) : 'Save Prompts'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"N24rFdBiZdwhxcqG0l6PHoDf2Ds=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription\n    ];\n});\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});