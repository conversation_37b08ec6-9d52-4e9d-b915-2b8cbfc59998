"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TrainingPage() {\n    _s();\n    // Subscription hook\n    const { subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation)();\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentCount, setDocumentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Fetch document count for a configuration\n    const fetchDocumentCount = async (configId)=>{\n        try {\n            const response = await fetch(\"/api/documents?config_id=\".concat(configId));\n            if (response.ok) {\n                var _data_documents;\n                const data = await response.json();\n                setDocumentCount(((_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.length) || 0);\n            }\n        } catch (err) {\n            console.error('Error fetching document count:', err);\n        }\n    };\n    // Fetch document count when config changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                fetchDocumentCount(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900\",\n                                    children: \"AI Training & Enhancement\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                subscriptionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierBadge, {\n                                    tier: subscriptionStatus.tier,\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl\",\n                            children: \"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-red-50 border border-red-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm font-medium\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-green-50 border border-green-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm font-medium\",\n                                children: successMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"knowledge_base\",\n                    customMessage: \"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-orange-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Knowledge Documents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            subscriptionStatus && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.LimitIndicator, {\n                                    current: documentCount,\n                                    limit: subscriptionStatus.tier === 'professional' ? 5 : subscriptionStatus.tier === 'enterprise' ? 999999 : 0,\n                                    label: \"Knowledge Base Documents\",\n                                    tier: subscriptionStatus.tier,\n                                    showUpgradeHint: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                configId: selectedConfigId,\n                                onDocumentUploaded: ()=>{\n                                    // Optionally refresh something or show a message\n                                    console.log('Document uploaded successfully');\n                                    // Refresh document count\n                                    if (selectedConfigId) {\n                                        fetchDocumentCount(selectedConfigId);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"prompt_engineering\",\n                    customMessage: \"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Define behavior, examples, and instructions for your AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"configSelect\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Select API Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"configSelect\",\n                                                value: selectedConfigId,\n                                                onChange: (e)=>setSelectedConfigId(e.target.value),\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Choose which model to train...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: config.id,\n                                                            children: config.name\n                                                        }, config.id, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"trainingPrompts\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Custom Prompts & Instructions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"trainingPrompts\",\n                                                value: trainingPrompts,\n                                                onChange: (e)=>setTrainingPrompts(e.target.value),\n                                                placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                                rows: 12,\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                        children: \"Training Format Guide:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-xs text-blue-800 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"SYSTEM:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Core instructions for the AI's role and personality\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"BEHAVIOR:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Guidelines for how the AI should behave\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Examples:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 375,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    ' Use \"User input → Expected response\" format'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"General:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Any other instructions written as normal text\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 376,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-secondary\",\n                                                    onClick: ()=>{\n                                                        if (confirm('Clear all training prompts?')) {\n                                                            setTrainingPrompts('');\n                                                        }\n                                                    },\n                                                    children: \"Clear Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleStartTraining,\n                                                disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                                className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Processing Prompts...\"\n                                                    ]\n                                                }, void 0, true) : 'Save Prompts'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"UrnJmCfw8iCy300JBOY9o5vKe/8=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation\n    ];\n});\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});