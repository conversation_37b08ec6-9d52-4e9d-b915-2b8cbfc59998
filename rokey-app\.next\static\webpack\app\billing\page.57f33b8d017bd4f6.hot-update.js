"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/billing/page",{

/***/ "(app-pages-browser)/./src/app/billing/page.tsx":
/*!**********************************!*\
  !*** ./src/app/billing/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BillingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CreditCardIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CalendarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowUpIcon,CalendarIcon,CheckCircleIcon,CreditCardIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst plans = [\n    {\n        id: 'free',\n        name: 'Free',\n        price: 0,\n        interval: 'forever',\n        features: [\n            {\n                name: 'Strict fallback routing only',\n                included: true\n            },\n            {\n                name: 'Basic analytics',\n                included: true\n            },\n            {\n                name: 'No custom roles',\n                included: false\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '1 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '3 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '3 max'\n            }\n        ]\n    },\n    {\n        id: 'starter',\n        name: 'Starter',\n        price: 19,\n        interval: 'month',\n        popular: true,\n        features: [\n            {\n                name: 'All routing strategies',\n                included: true\n            },\n            {\n                name: 'Advanced analytics',\n                included: true\n            },\n            {\n                name: 'Custom roles',\n                included: true,\n                limit: 'Up to 3 roles'\n            },\n            {\n                name: 'Configurations',\n                included: true,\n                limit: '5 max'\n            },\n            {\n                name: 'API keys per config',\n                included: true,\n                limit: '15 max'\n            },\n            {\n                name: 'User-generated API keys',\n                included: true,\n                limit: '50 max'\n            },\n            {\n                name: 'Browsing tasks',\n                included: true,\n                limit: '15/month'\n            }\n        ]\n    },\n    {\n        id: 'professional',\n        name: 'Professional',\n        price: 49,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Starter',\n                included: true\n            },\n            {\n                name: 'Unlimited configurations',\n                included: true\n            },\n            {\n                name: 'Unlimited API keys per config',\n                included: true\n            },\n            {\n                name: 'Unlimited user-generated API keys',\n                included: true\n            },\n            {\n                name: 'Knowledge base documents',\n                included: true,\n                limit: '5 documents'\n            },\n            {\n                name: 'Priority support',\n                included: true\n            }\n        ]\n    },\n    {\n        id: 'enterprise',\n        name: 'Enterprise',\n        price: 149,\n        interval: 'month',\n        features: [\n            {\n                name: 'Everything in Professional',\n                included: true\n            },\n            {\n                name: 'Unlimited knowledge base documents',\n                included: true\n            },\n            {\n                name: 'Advanced semantic caching',\n                included: true\n            },\n            {\n                name: 'Custom integrations',\n                included: true\n            },\n            {\n                name: 'Dedicated support + phone',\n                included: true\n            },\n            {\n                name: 'SLA guarantee',\n                included: true\n            }\n        ]\n    }\n];\nfunction BillingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, subscriptionStatus, refreshSubscription } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelReason, setCancelReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cancelFeedback, setCancelFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const currentPlan = plans.find((plan)=>plan.id === (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier)) || plans[0];\n    // Calculate days until renewal (mock data for now)\n    const daysUntilRenewal = (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' ? Math.floor(Math.random() * 30) + 1 : null;\n    const handlePlanChange = async (planId)=>{\n        if (planId === currentPlan.id) return;\n        const targetPlan = plans.find((p)=>p.id === planId);\n        if (!targetPlan) return;\n        const isUpgrade = plans.findIndex((p)=>p.id === planId) > plans.findIndex((p)=>p.id === currentPlan.id);\n        confirmation.showConfirmation({\n            title: isUpgrade ? 'Upgrade Plan' : 'Downgrade Plan',\n            message: \"Are you sure you want to \".concat(isUpgrade ? 'upgrade' : 'downgrade', \" to the \").concat(targetPlan.name, \" plan?\"),\n            confirmText: isUpgrade ? 'Upgrade Now' : 'Downgrade',\n            cancelText: 'Cancel',\n            type: isUpgrade ? 'info' : 'warning'\n        }, async ()=>{\n            setLoading(true);\n            try {\n                // Here you would integrate with your payment processor\n                // For now, we'll simulate the process\n                await new Promise((resolve)=>setTimeout(resolve, 2000));\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success(\"Successfully \".concat(isUpgrade ? 'upgraded' : 'downgraded', \" to \").concat(targetPlan.name, \" plan\"));\n                await refreshSubscription();\n            } catch (error) {\n                console.error('Plan change error:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to change plan. Please try again.');\n            } finally{\n                setLoading(false);\n            }\n        });\n    };\n    const handleCancelSubscription = async ()=>{\n        if (!cancelReason.trim()) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Please select a reason for cancellation');\n            return;\n        }\n        setLoading(true);\n        try {\n            var _user_user_metadata;\n            // Send cancellation feedback via EmailJS\n            const templateParams = {\n                user_email: (user === null || user === void 0 ? void 0 : user.email) || 'Unknown',\n                user_name: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.first_name) || 'User',\n                current_plan: currentPlan.name,\n                cancel_reason: cancelReason,\n                additional_feedback: cancelFeedback,\n                cancel_date: new Date().toLocaleDateString()\n            };\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_10__[\"default\"].send(\"service_2xtn7iv\", \"template_pg7e1af\", templateParams, \"lm7-ATth2Cql60KIN\");\n            // Here you would also cancel the subscription in your payment processor\n            // For now, we'll simulate the process\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('Subscription cancelled successfully. We\\'ve sent your feedback to our team.');\n            setShowCancelModal(false);\n            setCancelReason('');\n            setCancelFeedback('');\n            await refreshSubscription();\n        } catch (error) {\n            console.error('Cancellation error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('Failed to cancel subscription. Please contact support.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const cancelReasons = [\n        'Too expensive',\n        'Not using enough features',\n        'Found a better alternative',\n        'Technical issues',\n        'Poor customer support',\n        'Missing features I need',\n        'Temporary financial constraints',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Billing & Plans\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Manage your subscription, billing information, and plan features.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Current Plan\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-2xl font-bold text-gray-900\",\n                                                                children: currentPlan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            currentPlan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-orange-100 text-orange-800\",\n                                                                children: \"Popular\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mt-1\",\n                                                        children: currentPlan.price === 0 ? 'Free forever' : \"$\".concat(currentPlan.price, \"/\").concat(currentPlan.interval)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    daysUntilRenewal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mt-2 text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"Renews in \",\n                                                                    daysUntilRenewal,\n                                                                    \" days\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: (subscriptionStatus === null || subscriptionStatus === void 0 ? void 0 : subscriptionStatus.tier) !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: ()=>setShowCancelModal(true),\n                                                    className: \"text-red-600 border-red-200 hover:bg-red-50\",\n                                                    children: \"Cancel Subscription\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                    children: \"Available Plans\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                    children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                            className: \"relative \".concat(plan.popular ? 'ring-2 ring-orange-500' : ''),\n                                            children: [\n                                                plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-3 left-1/2 transform -translate-x-1/2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"bg-orange-500 text-white\",\n                                                        children: \"Most Popular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xl font-bold\",\n                                                                children: plan.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-3xl font-bold\",\n                                                                        children: [\n                                                                            \"$\",\n                                                                            plan.price\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    plan.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-500\",\n                                                                        children: [\n                                                                            \"/\",\n                                                                            plan.interval\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 273,\n                                                                        columnNumber: 44\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-3 mb-6\",\n                                                            children: plan.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        feature.included ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-green-500 flex-shrink-0 mt-0.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-300 flex-shrink-0 mt-0.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                            lineNumber: 284,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm \".concat(feature.included ? 'text-gray-900' : 'text-gray-400'),\n                                                                            children: [\n                                                                                feature.name,\n                                                                                feature.limit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-gray-500\",\n                                                                                    children: [\n                                                                                        \" (\",\n                                                                                        feature.limit,\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                                    lineNumber: 289,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        plan.id === currentPlan.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            disabled: true,\n                                                            className: \"w-full\",\n                                                            children: \"Current Plan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            onClick: ()=>handlePlanChange(plan.id),\n                                                            disabled: loading,\n                                                            className: \"w-full\",\n                                                            variant: plan.popular ? \"default\" : \"outline\",\n                                                            children: plans.findIndex((p)=>p.id === plan.id) > plans.findIndex((p)=>p.id === currentPlan.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 309,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Upgrade to \",\n                                                                    plan.name\n                                                                ]\n                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Downgrade to \",\n                                                                    plan.name\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, plan.id, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                showCancelModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-2xl max-w-md w-full p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowUpIcon_CalendarIcon_CheckCircleIcon_CreditCardIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Cancel Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-4\",\n                                children: \"We're sorry to see you go! Please help us improve by telling us why you're cancelling.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Reason for cancellation *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: cancelReason,\n                                                onChange: (e)=>setCancelReason(e.target.value),\n                                                className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Select a reason...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    cancelReasons.map((reason)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: reason,\n                                                            children: reason\n                                                        }, reason, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Additional feedback (optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: cancelFeedback,\n                                                onChange: (e)=>setCancelFeedback(e.target.value),\n                                                placeholder: \"Tell us more about your experience or what we could do better...\",\n                                                rows: 3,\n                                                className: \"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setShowCancelModal(false),\n                                        className: \"flex-1\",\n                                        children: \"Keep Subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: handleCancelSubscription,\n                                        disabled: loading || !cancelReason.trim(),\n                                        className: \"flex-1 bg-red-600 hover:bg-red-700\",\n                                        children: loading ? 'Cancelling...' : 'Cancel Subscription'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\billing\\\\page.tsx\",\n        lineNumber: 204,\n        columnNumber: 5\n    }, this);\n}\n_s(BillingPage, \"8t8Xdg1AWd4vpEyzMTNFZrwzpWs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_7__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_9__.useConfirmation\n    ];\n});\n_c = BillingPage;\nvar _c;\n$RefreshReg$(_c, \"BillingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/billing/page.tsx\n"));

/***/ })

});