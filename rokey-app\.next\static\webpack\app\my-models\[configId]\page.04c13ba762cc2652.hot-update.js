"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/[configId]/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/LimitIndicator.tsx":
/*!***********************************************************!*\
  !*** ./src/components/TierEnforcement/LimitIndicator.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LimitIndicator: () => (/* binding */ LimitIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ LimitIndicator auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction LimitIndicator(param) {\n    let { current, limit, label, tier, showUpgradeHint = true, className = '' } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isUnlimited = limit >= 999999;\n    const percentage = isUnlimited ? 0 : current / limit * 100;\n    const isNearLimit = percentage >= 80;\n    const isAtLimit = current >= limit && !isUnlimited;\n    const getStatusColor = ()=>{\n        if (isUnlimited) return 'text-green-600';\n        if (isAtLimit) return 'text-red-600';\n        if (isNearLimit) return 'text-yellow-600';\n        return 'text-green-600';\n    };\n    const getStatusIcon = ()=>{\n        if (isUnlimited) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 40,\n            columnNumber: 29\n        }, this);\n        if (isAtLimit) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-red-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 41,\n            columnNumber: 27\n        }, this);\n        if (isNearLimit) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-5 h-5 text-yellow-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 42,\n            columnNumber: 29\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 43,\n            columnNumber: 12\n        }, this);\n    };\n    const getProgressBarColor = ()=>{\n        if (isUnlimited) return 'bg-green-500';\n        if (isAtLimit) return 'bg-red-500';\n        if (isNearLimit) return 'bg-yellow-500';\n        return 'bg-green-500';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between text-sm \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            label,\n                            \":\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    !isUnlimited && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 bg-gray-200 rounded-full h-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            className: \"h-1.5 rounded-full \".concat(getProgressBarColor()),\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"\".concat(Math.min(percentage, 100), \"%\")\n                            },\n                            transition: {\n                                duration: 0.5,\n                                ease: \"easeOut\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-medium \".concat(getStatusColor()),\n                        children: isUnlimited ? 'Unlimited' : \"\".concat(current, \"/\").concat(limit)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    (isAtLimit || isNearLimit) && showUpgradeHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-xs text-orange-600 hover:text-orange-700 underline ml-2\",\n                        onClick: ()=>{\n                            router.push('/billing');\n                        },\n                        children: \"Upgrade\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(LimitIndicator, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LimitIndicator;\nvar _c;\n$RefreshReg$(_c, \"LimitIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/LimitIndicator.tsx\n"));

/***/ })

});