/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/documents/upload/route";
exports.ids = ["app/api/documents/upload/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Fupload%2Froute&page=%2Fapi%2Fdocuments%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Fupload%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Fupload%2Froute&page=%2Fapi%2Fdocuments%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Fupload%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_documents_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/documents/upload/route.ts */ \"(rsc)/./src/app/api/documents/upload/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/documents/upload/route\",\n        pathname: \"/api/documents/upload\",\n        filename: \"route\",\n        bundlePath: \"app/api/documents/upload/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\documents\\\\upload\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_documents_upload_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Fupload%2Froute&page=%2Fapi%2Fdocuments%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Fupload%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/documents/upload/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/documents/upload/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(rsc)/./src/lib/stripe-client.ts\");\n/* harmony import */ var _langchain_textsplitters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @langchain/textsplitters */ \"(rsc)/./node_modules/@langchain/textsplitters/index.js\");\n/* harmony import */ var _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/embeddings/jina */ \"(rsc)/./src/lib/embeddings/jina.ts\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! os */ \"os\");\n/* harmony import */ var os__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(os__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var pdf_parse__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! pdf-parse */ \"pdf-parse\");\n/* harmony import */ var pdf_parse__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(pdf_parse__WEBPACK_IMPORTED_MODULE_8__);\n\n\n\n\n\n\n\n\n\n// Text splitter for chunking documents - improved for better context preservation\nconst textSplitter = new _langchain_textsplitters__WEBPACK_IMPORTED_MODULE_3__.RecursiveCharacterTextSplitter({\n    chunkSize: 1500,\n    chunkOverlap: 300,\n    separators: [\n        '\\n\\n',\n        '\\n',\n        '. ',\n        '! ',\n        '? ',\n        '; ',\n        ', ',\n        ' ',\n        ''\n    ]\n});\nasync function POST(request) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        // Check authentication\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Parse form data\n        const formData = await request.formData();\n        const file = formData.get('file');\n        const configId = formData.get('configId');\n        if (!file || !configId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'File and configId are required'\n            }, {\n                status: 400\n            });\n        }\n        // Check user's subscription tier for knowledge base access\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Check if user has access to knowledge base feature\n        if (!(0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.hasFeatureAccess)(userTier, 'knowledge_base')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Knowledge base is not available on the ${userTier} plan. Please upgrade to upload documents.`\n            }, {\n                status: 403\n            });\n        }\n        // Count current documents for this user and config\n        const { count: currentDocCount } = await supabase.from('documents').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', user.id).eq('custom_api_config_id', configId);\n        // Check document limits based on tier\n        const tierConfig = (0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.getTierConfig)(userTier);\n        const maxDocs = tierConfig.limits.knowledgeBaseDocuments;\n        if (maxDocs !== 999999 && (currentDocCount || 0) >= maxDocs) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `You have reached the maximum number of documents (${maxDocs}) for your ${userTier} plan. Please upgrade to upload more documents.`\n            }, {\n                status: 403\n            });\n        }\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unsupported file type. Please upload PDF, TXT, or MD files.'\n            }, {\n                status: 400\n            });\n        }\n        // Validate file size (max 10MB)\n        const maxSize = 10 * 1024 * 1024; // 10MB\n        if (file.size > maxSize) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'File size too large. Maximum size is 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        console.log(`[Document Upload] Processing file: ${file.name}, type: ${file.type}, size: ${file.size}`);\n        // Create temporary file\n        const bytes = await file.arrayBuffer();\n        const buffer = Buffer.from(bytes);\n        const tempFilePath = (0,path__WEBPACK_IMPORTED_MODULE_6__.join)((0,os__WEBPACK_IMPORTED_MODULE_7__.tmpdir)(), `upload_${Date.now()}_${file.name}`);\n        await (0,fs_promises__WEBPACK_IMPORTED_MODULE_5__.writeFile)(tempFilePath, buffer);\n        let extractedContent = '';\n        try {\n            // Load document based on type\n            switch(file.type){\n                case 'application/pdf':\n                    // Suppress pdf-parse warnings by temporarily capturing console.warn\n                    const originalWarn = console.warn;\n                    console.warn = (message)=>{\n                        // Only suppress the specific font warning, allow other warnings through\n                        if (typeof message === 'string' && message.includes('Ran out of space in font private use area')) {\n                            return;\n                        }\n                        originalWarn(message);\n                    };\n                    try {\n                        const pdfData = await pdf_parse__WEBPACK_IMPORTED_MODULE_8___default()(buffer, {\n                            // Add options to improve parsing\n                            max: 0 // No limit on pages\n                        });\n                        extractedContent = pdfData.text;\n                        // Check if we got any text\n                        if (!extractedContent || extractedContent.trim().length === 0) {\n                            console.warn('[Document Upload] No text extracted from PDF, trying alternative approach...');\n                            // Try with different options as fallback\n                            const fallbackData = await pdf_parse__WEBPACK_IMPORTED_MODULE_8___default()(buffer, {\n                                max: 0\n                            });\n                            extractedContent = fallbackData.text;\n                            if (!extractedContent || extractedContent.trim().length === 0) {\n                                throw new Error('No text could be extracted from this PDF. The file may be image-based, password-protected, or corrupted.');\n                            }\n                        }\n                        // Log some useful info about the PDF\n                        console.log(`[Document Upload] PDF processed: ${pdfData.numpages || 'unknown'} pages, ${extractedContent.length} characters`);\n                    } catch (pdfError) {\n                        console.error('[Document Upload] PDF parsing error:', pdfError);\n                        throw new Error(`Failed to process PDF: ${pdfError.message || 'Unknown error'}`);\n                    } finally{\n                        // Restore original console.warn\n                        console.warn = originalWarn;\n                    }\n                    break;\n                case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n                    // For DOCX, we'll need a different approach since we removed DocxLoader\n                    // For now, let's throw an error and handle this separately\n                    throw new Error('DOCX support temporarily disabled. Please use PDF, TXT, or MD files.');\n                case 'text/plain':\n                case 'text/markdown':\n                    extractedContent = await (0,fs_promises__WEBPACK_IMPORTED_MODULE_5__.readFile)(tempFilePath, 'utf-8');\n                    break;\n                default:\n                    throw new Error('Unsupported file type');\n            }\n            console.log(`[Document Upload] Extracted ${extractedContent.length} characters from ${file.name}`);\n            // Create a document object for text splitting\n            const document = {\n                pageContent: extractedContent,\n                metadata: {\n                    source: file.name,\n                    type: file.type\n                }\n            };\n            // Split document into chunks\n            const chunks = await textSplitter.splitDocuments([\n                document\n            ]);\n            console.log(`[Document Upload] Split into ${chunks.length} chunks`);\n            // Store document metadata in database\n            const { data: documentRecord, error: docError } = await supabase.from('documents').insert({\n                user_id: user.id,\n                custom_api_config_id: configId,\n                filename: file.name,\n                file_type: file.type,\n                file_size: file.size,\n                content: extractedContent,\n                metadata: {\n                    chunks_count: chunks.length,\n                    processing_started_at: new Date().toISOString()\n                },\n                status: 'processing'\n            }).select().single();\n            if (docError) {\n                console.error('[Document Upload] Error storing document:', docError);\n                throw new Error('Failed to store document metadata');\n            }\n            console.log(`[Document Upload] Stored document record: ${documentRecord.id}`);\n            // Test embedding generation with a small sample first\n            try {\n                console.log(`[Document Upload] Testing Jina embedding generation...`);\n                const testEmbedding = await _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_4__.jinaEmbeddings.embedQuery(\"test\");\n                console.log(`[Document Upload] Jina embedding test successful (${testEmbedding.length} dimensions)`);\n                console.log(`[Document Upload] Database expects 1024 dimensions, Jina v3 produces ${testEmbedding.length} dimensions`);\n                if (testEmbedding.length !== 1024) {\n                    throw new Error(`Dimension mismatch: Database expects 1024 dimensions but Jina v3 produces ${testEmbedding.length} dimensions`);\n                }\n            } catch (embeddingError) {\n                console.error(`[Document Upload] Jina embedding test failed:`, embeddingError);\n                throw new Error(`Jina embedding generation failed: ${embeddingError.message}`);\n            }\n            // Process chunks and generate embeddings\n            const chunkPromises = chunks.map(async (chunk, index)=>{\n                try {\n                    console.log(`[Document Upload] Processing chunk ${index + 1}/${chunks.length} (${chunk.pageContent.length} chars)`);\n                    // Generate embedding for this chunk using Jina v3\n                    const embedding = await _lib_embeddings_jina__WEBPACK_IMPORTED_MODULE_4__.jinaEmbeddings.embedQuery(chunk.pageContent);\n                    console.log(`[Document Upload] Generated Jina embedding for chunk ${index} (${embedding.length} dimensions)`);\n                    // Prepare chunk data\n                    const chunkData = {\n                        document_id: documentRecord.id,\n                        user_id: user.id,\n                        custom_api_config_id: configId,\n                        content: chunk.pageContent,\n                        metadata: {\n                            ...chunk.metadata,\n                            chunk_index: index,\n                            chunk_size: chunk.pageContent.length\n                        },\n                        embedding: embedding\n                    };\n                    // Store chunk with embedding\n                    const { data: insertedChunk, error: chunkError } = await supabase.from('document_chunks').insert(chunkData).select().single();\n                    if (chunkError) {\n                        console.error(`[Document Upload] Error storing chunk ${index}:`, chunkError);\n                        console.error(`[Document Upload] Chunk data:`, {\n                            document_id: documentRecord.id,\n                            content_length: chunk.pageContent.length,\n                            embedding_length: embedding.length\n                        });\n                        throw chunkError;\n                    }\n                    console.log(`[Document Upload] Successfully stored chunk ${index} with ID: ${insertedChunk.id}`);\n                    return {\n                        success: true,\n                        index,\n                        chunkId: insertedChunk.id\n                    };\n                } catch (error) {\n                    console.error(`[Document Upload] Error processing chunk ${index}:`, error);\n                    return {\n                        success: false,\n                        index,\n                        error: error.message || error\n                    };\n                }\n            });\n            // Wait for all chunks to be processed\n            const chunkResults = await Promise.all(chunkPromises);\n            const successfulChunks = chunkResults.filter((r)=>r.success).length;\n            const failedChunks = chunkResults.filter((r)=>!r.success).length;\n            const failedChunkDetails = chunkResults.filter((r)=>!r.success);\n            console.log(`[Document Upload] Processed ${successfulChunks}/${chunks.length} chunks successfully`);\n            if (failedChunks > 0) {\n                console.error(`[Document Upload] Failed chunks:`, failedChunkDetails);\n            }\n            // Update document status\n            const finalStatus = failedChunks === 0 ? 'completed' : 'failed';\n            const { error: updateError } = await supabase.from('documents').update({\n                status: finalStatus,\n                metadata: {\n                    ...documentRecord.metadata,\n                    chunks_processed: successfulChunks,\n                    chunks_failed: failedChunks,\n                    processing_completed_at: new Date().toISOString(),\n                    ...failedChunks > 0 && {\n                        failed_chunk_errors: failedChunkDetails.slice(0, 5)\n                    } // Store first 5 errors\n                },\n                updated_at: new Date().toISOString()\n            }).eq('id', documentRecord.id);\n            if (updateError) {\n                console.error(`[Document Upload] Error updating document status:`, updateError);\n            }\n            // Clean up temporary file\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_5__.unlink)(tempFilePath);\n            // Invalidate training cache to ensure immediate effect\n            try {\n                const { trainingDataCache } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_cache_trainingCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/cache/trainingCache */ \"(rsc)/./src/lib/cache/trainingCache.ts\"));\n                trainingDataCache.invalidate(configId);\n                console.log(`[Document Upload] Cache invalidated for config: ${configId}`);\n            } catch (error) {\n                console.warn('[Document Upload] Cache invalidation failed:', error);\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                document: {\n                    id: documentRecord.id,\n                    filename: file.name,\n                    status: finalStatus,\n                    chunks_processed: successfulChunks,\n                    chunks_total: chunks.length\n                }\n            });\n        } catch (processingError) {\n            console.error('[Document Upload] Processing error:', processingError);\n            // Clean up temporary file\n            try {\n                await (0,fs_promises__WEBPACK_IMPORTED_MODULE_5__.unlink)(tempFilePath);\n            } catch (unlinkError) {\n                console.warn('[Document Upload] Failed to clean up temp file:', unlinkError);\n            }\n            throw processingError;\n        }\n    } catch (error) {\n        console.error('[Document Upload] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process document',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/documents/upload/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/embeddings/jina.ts":
/*!************************************!*\
  !*** ./src/lib/embeddings/jina.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiKeyJinaEmbeddings: () => (/* binding */ MultiKeyJinaEmbeddings),\n/* harmony export */   jinaEmbeddings: () => (/* binding */ jinaEmbeddings)\n/* harmony export */ });\n/**\r\n * Multi-Key Jina Embeddings v3 Implementation\r\n * Provides automatic key rotation and high rate limits for RouKey\r\n */ class MultiKeyJinaEmbeddings {\n    constructor(){\n        this.currentKeyIndex = 0;\n        this.keyUsage = new Map();\n        this.baseUrl = 'https://api.jina.ai/v1/embeddings';\n        this.model = 'jina-embeddings-v3';\n        // Load all Jina API keys from environment\n        this.apiKeys = [\n            process.env.JINA_API_KEY,\n            process.env.JINA_API_KEY_2,\n            process.env.JINA_API_KEY_3,\n            process.env.JINA_API_KEY_4,\n            process.env.JINA_API_KEY_5,\n            process.env.JINA_API_KEY_6,\n            process.env.JINA_API_KEY_7,\n            process.env.JINA_API_KEY_9,\n            process.env.JINA_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Jina API keys found in environment variables');\n        }\n        console.log(`[Jina Embeddings] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage stats for each key\n        this.apiKeys.forEach((key)=>{\n            this.keyUsage.set(key, {\n                requests: 0,\n                tokens: 0,\n                lastUsed: new Date(),\n                errors: 0\n            });\n        });\n    }\n    /**\r\n   * Get the next API key using round-robin rotation\r\n   */ getNextKey() {\n        const key = this.apiKeys[this.currentKeyIndex];\n        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;\n        return key;\n    }\n    /**\r\n   * Get the best available API key based on usage and error rates\r\n   */ getBestKey() {\n        // For now, use simple round-robin\n        // TODO: Implement smart selection based on usage stats\n        return this.getNextKey();\n    }\n    /**\r\n   * Update usage statistics for a key\r\n   */ updateKeyUsage(apiKey, tokens, isError = false) {\n        const stats = this.keyUsage.get(apiKey);\n        if (stats) {\n            stats.requests++;\n            stats.tokens += tokens;\n            stats.lastUsed = new Date();\n            if (isError) {\n                stats.errors++;\n                stats.lastError = new Date();\n            }\n        }\n    }\n    /**\r\n   * Generate embedding for a single text input\r\n   */ async embedQuery(text) {\n        const maxRetries = this.apiKeys.length;\n        let lastError = null;\n        for(let attempt = 0; attempt < maxRetries; attempt++){\n            try {\n                const apiKey = this.getBestKey();\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1}/${maxRetries} with key ${this.apiKeys.indexOf(apiKey) + 1}`);\n                const response = await fetch(this.baseUrl, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        model: this.model,\n                        input: [\n                            text\n                        ],\n                        normalized: true,\n                        embedding_type: 'float'\n                    })\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    if (response.status === 429) {\n                        console.log(`[Jina Embeddings] Rate limit hit for key ${this.apiKeys.indexOf(apiKey) + 1}, trying next key...`);\n                        this.updateKeyUsage(apiKey, 0, true);\n                        continue;\n                    }\n                    throw new Error(`HTTP ${response.status}: ${errorText}`);\n                }\n                const data = await response.json();\n                if (!data.data || data.data.length === 0) {\n                    throw new Error('No embedding data returned from Jina API');\n                }\n                const embedding = data.data[0].embedding;\n                // Update usage stats\n                this.updateKeyUsage(apiKey, data.usage?.total_tokens || text.length);\n                console.log(`[Jina Embeddings] Success with key ${this.apiKeys.indexOf(apiKey) + 1} (${embedding.length} dimensions, ${data.usage?.total_tokens || 'unknown'} tokens)`);\n                return embedding;\n            } catch (error) {\n                lastError = error;\n                console.log(`[Jina Embeddings] Attempt ${attempt + 1} failed:`, error.message);\n                // If this is the last attempt, throw the error\n                if (attempt === maxRetries - 1) {\n                    break;\n                }\n            }\n        }\n        // All keys failed\n        console.error(`[Jina Embeddings] All ${maxRetries} API keys failed`);\n        throw new Error(`All Jina API keys failed. Last error: ${lastError?.message || 'Unknown error'}`);\n    }\n    /**\r\n   * Generate embeddings for multiple texts (batch processing)\r\n   */ async embedDocuments(texts) {\n        // For now, process sequentially to avoid overwhelming the API\n        // TODO: Implement smart batching based on rate limits\n        const embeddings = [];\n        for(let i = 0; i < texts.length; i++){\n            console.log(`[Jina Embeddings] Processing document ${i + 1}/${texts.length}`);\n            const embedding = await this.embedQuery(texts[i]);\n            embeddings.push(embedding);\n            // Small delay to respect rate limits\n            if (i < texts.length - 1) {\n                await new Promise((resolve)=>setTimeout(resolve, 100));\n            }\n        }\n        return embeddings;\n    }\n    /**\r\n   * Get usage statistics for all keys\r\n   */ getUsageStats() {\n        const stats = {};\n        this.apiKeys.forEach((key, index)=>{\n            const keyStats = this.keyUsage.get(key);\n            if (keyStats) {\n                stats[`key_${index + 1}`] = {\n                    ...keyStats\n                };\n            }\n        });\n        return stats;\n    }\n    /**\r\n   * Get total capacity across all keys\r\n   */ getTotalCapacity() {\n        return {\n            totalKeys: this.apiKeys.length,\n            estimatedRPM: this.apiKeys.length * 500,\n            estimatedTokensPerMonth: this.apiKeys.length * 1000000\n        };\n    }\n}\n// Export a singleton instance\nconst jinaEmbeddings = new MultiKeyJinaEmbeddings();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/embeddings/jina.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$19',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '4 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback + Complex routing (1 config limit)',\n            'Up to 3 custom roles',\n            'Intelligent role routing (1 config)',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 4,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$49',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            '20 Custom Configurations',\n            '15 API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 20,\n            apiKeysPerConfig: 15,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: '$149',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.ENTERPRISE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.ENTERPRISE,\n        features: [\n            'Unlimited API requests',\n            'Unlimited configurations',\n            'Unlimited API keys',\n            'All 300+ models + priority access',\n            'All routing strategies',\n            'Unlimited custom roles',\n            'All features + priority support',\n            'Unlimited knowledge base documents',\n            'Advanced semantic caching',\n            'Custom integrations',\n            'Dedicated support + phone',\n            'SLA guarantee'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 999999,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        case 'configurations':\n            return limits.configurations > 0; // All tiers can create at least some configurations\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3NlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVFO0FBQ2hDO0FBR3ZDLG1FQUFtRTtBQUNuRSw2RUFBNkU7QUFDN0UsbURBQW1EO0FBQzVDLGVBQWVFO0lBQ3BCLE1BQU1DLGNBQWMsTUFBTUYscURBQU9BO0lBRWpDLE9BQU9ELGlFQUFrQkEsQ0FDdkJJLDBDQUFvQyxFQUNwQ0Esa05BQXlDLEVBQ3pDO1FBQ0VILFNBQVM7WUFDUE8sS0FBSUMsSUFBWTtnQkFDZCxPQUFPTixZQUFZSyxHQUFHLENBQUNDLE9BQU9DO1lBQ2hDO1lBQ0FDLEtBQUlGLElBQVksRUFBRUMsS0FBYSxFQUFFRSxPQUFzQjtnQkFDckQsSUFBSTtvQkFDRlQsWUFBWVEsR0FBRyxDQUFDO3dCQUFFRjt3QkFBTUM7d0JBQU8sR0FBR0UsT0FBTztvQkFBQztnQkFDNUMsRUFBRSxPQUFPQyxPQUFPO29CQUNkLDZEQUE2RDtvQkFDN0QsZ0VBQWdFO29CQUNoRSwrQ0FBK0M7b0JBQy9DQyxRQUFRQyxJQUFJLENBQUMsQ0FBQyxzQkFBc0IsRUFBRU4sS0FBSyxtQ0FBbUMsQ0FBQyxFQUFFSTtnQkFDbkY7WUFDRjtZQUNBRyxRQUFPUCxJQUFZLEVBQUVHLE9BQXNCO2dCQUN6QyxJQUFJO29CQUNGLGlFQUFpRTtvQkFDakUsd0ZBQXdGO29CQUN4RlQsWUFBWVEsR0FBRyxDQUFDO3dCQUFFRjt3QkFBTUMsT0FBTzt3QkFBSSxHQUFHRSxPQUFPO29CQUFDO2dCQUNoRCxFQUFFLE9BQU9DLE9BQU87b0JBQ2QseURBQXlEO29CQUN6REMsUUFBUUMsSUFBSSxDQUFDLENBQUMseUJBQXlCLEVBQUVOLEtBQUssbUNBQW1DLENBQUMsRUFBRUk7Z0JBQ3RGO1lBQ0Y7UUFDRjtJQUNGO0FBRUo7QUFFQSw2RUFBNkU7QUFDdEUsU0FBU0ksc0NBQXNDQyxPQUFvQjtJQUN4RSxPQUFPbEIsaUVBQWtCQSxDQUN2QkksMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUgsU0FBUztZQUNQTyxLQUFJQyxJQUFZO2dCQUNkLE9BQU9TLFFBQVFqQixPQUFPLENBQUNPLEdBQUcsQ0FBQ0MsT0FBT0M7WUFDcEM7WUFDQUMsS0FBSUYsSUFBWSxFQUFFQyxLQUFhLEVBQUVFLE9BQXNCO1lBQ3JELDhEQUE4RDtZQUM5RCx1Q0FBdUM7WUFDekM7WUFDQUksUUFBT1AsSUFBWSxFQUFFRyxPQUFzQjtZQUN6QyxpRUFBaUU7WUFDakUsdUNBQXVDO1lBQ3pDO1FBQ0Y7SUFDRjtBQUVKIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcbGliXFxzdXBhYmFzZVxcc2VydmVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVNlcnZlckNsaWVudCwgdHlwZSBDb29raWVPcHRpb25zIH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XHJcbmltcG9ydCB7IGNvb2tpZXMgfSBmcm9tICduZXh0L2hlYWRlcnMnO1xyXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gJ25leHQvc2VydmVyJztcclxuXHJcbi8vIFRoaXMgaXMgdGhlIHN0YW5kYXJkIHNldHVwIGZvciBjcmVhdGluZyBhIFN1cGFiYXNlIHNlcnZlciBjbGllbnRcclxuLy8gaW4gTmV4dC5qcyBBcHAgUm91dGVyIChTZXJ2ZXIgQ29tcG9uZW50cywgUm91dGUgSGFuZGxlcnMsIFNlcnZlciBBY3Rpb25zKS5cclxuLy8gVXBkYXRlZCBmb3IgTmV4dC5qcyAxNSBhc3luYyBjb29raWVzIHJlcXVpcmVtZW50XHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudE9uUmVxdWVzdCgpIHtcclxuICBjb25zdCBjb29raWVTdG9yZSA9IGF3YWl0IGNvb2tpZXMoKTtcclxuXHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXHJcbiAgICB7XHJcbiAgICAgIGNvb2tpZXM6IHtcclxuICAgICAgICBnZXQobmFtZTogc3RyaW5nKSB7XHJcbiAgICAgICAgICByZXR1cm4gY29va2llU3RvcmUuZ2V0KG5hbWUpPy52YWx1ZTtcclxuICAgICAgICB9LFxyXG4gICAgICAgIHNldChuYW1lOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcsIG9wdGlvbnM6IENvb2tpZU9wdGlvbnMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvb2tpZVN0b3JlLnNldCh7IG5hbWUsIHZhbHVlLCAuLi5vcHRpb25zIH0pO1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gVGhpcyBlcnJvciBjYW4gYmUgaWdub3JlZCBpZiBydW5uaW5nIGluIGEgU2VydmVyIENvbXBvbmVudFxyXG4gICAgICAgICAgICAvLyB3aGVyZSBjb29raWVzIGNhbid0IGJlIHNldCBkaXJlY3RseS4gQ29va2llIHNldHRpbmcgc2hvdWxkIGJlXHJcbiAgICAgICAgICAgIC8vIGhhbmRsZWQgaW4gU2VydmVyIEFjdGlvbnMgb3IgUm91dGUgSGFuZGxlcnMuXHJcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHNldCBjb29raWUgJyR7bmFtZX0nIChtaWdodCBiZSBpbiBhIFNlcnZlciBDb21wb25lbnQpOmAsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIHJlbW92ZShuYW1lOiBzdHJpbmcsIG9wdGlvbnM6IENvb2tpZU9wdGlvbnMpIHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIC8vIFRvIHJlbW92ZSBhIGNvb2tpZSB1c2luZyB0aGUgYHNldGAgbWV0aG9kIGZyb20gYG5leHQvaGVhZGVyc2AsXHJcbiAgICAgICAgICAgIC8vIHlvdSB0eXBpY2FsbHkgc2V0IGl0IHdpdGggYW4gZW1wdHkgdmFsdWUgYW5kIE1heC1BZ2U9MCBvciBhbiBleHBpcnkgZGF0ZSBpbiB0aGUgcGFzdC5cclxuICAgICAgICAgICAgY29va2llU3RvcmUuc2V0KHsgbmFtZSwgdmFsdWU6ICcnLCAuLi5vcHRpb25zIH0pO1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgLy8gU2ltaWxhciB0byBzZXQsIHRoaXMgbWlnaHQgZmFpbCBpbiBhIFNlcnZlciBDb21wb25lbnQuXHJcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIHJlbW92ZSBjb29raWUgJyR7bmFtZX0nIChtaWdodCBiZSBpbiBhIFNlcnZlciBDb21wb25lbnQpOmAsIGVycm9yKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICB9LFxyXG4gICAgfVxyXG4gICk7XHJcbn1cclxuXHJcbi8vIEFsdGVybmF0aXZlIG1ldGhvZCBmb3IgQVBJIHJvdXRlcyB0aGF0IG5lZWQgdG8gaGFuZGxlIGNvb2tpZXMgZnJvbSByZXF1ZXN0XHJcbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudEZyb21SZXF1ZXN0KHJlcXVlc3Q6IE5leHRSZXF1ZXN0KSB7XHJcbiAgcmV0dXJuIGNyZWF0ZVNlcnZlckNsaWVudChcclxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXHJcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXHJcbiAgICB7XHJcbiAgICAgIGNvb2tpZXM6IHtcclxuICAgICAgICBnZXQobmFtZTogc3RyaW5nKSB7XHJcbiAgICAgICAgICByZXR1cm4gcmVxdWVzdC5jb29raWVzLmdldChuYW1lKT8udmFsdWU7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBzZXQobmFtZTogc3RyaW5nLCB2YWx1ZTogc3RyaW5nLCBvcHRpb25zOiBDb29raWVPcHRpb25zKSB7XHJcbiAgICAgICAgICAvLyBJbiBBUEkgcm91dGVzLCB3ZSBjYW4ndCBzZXQgY29va2llcyBkaXJlY3RseSBvbiB0aGUgcmVxdWVzdFxyXG4gICAgICAgICAgLy8gVGhpcyB3aWxsIGJlIGhhbmRsZWQgYnkgdGhlIHJlc3BvbnNlXHJcbiAgICAgICAgfSxcclxuICAgICAgICByZW1vdmUobmFtZTogc3RyaW5nLCBvcHRpb25zOiBDb29raWVPcHRpb25zKSB7XHJcbiAgICAgICAgICAvLyBJbiBBUEkgcm91dGVzLCB3ZSBjYW4ndCByZW1vdmUgY29va2llcyBkaXJlY3RseSBvbiB0aGUgcmVxdWVzdFxyXG4gICAgICAgICAgLy8gVGhpcyB3aWxsIGJlIGhhbmRsZWQgYnkgdGhlIHJlc3BvbnNlXHJcbiAgICAgICAgfSxcclxuICAgICAgfSxcclxuICAgIH1cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVTZXJ2ZXJDbGllbnQiLCJjb29raWVzIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRPblJlcXVlc3QiLCJjb29raWVTdG9yZSIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSIsImdldCIsIm5hbWUiLCJ2YWx1ZSIsInNldCIsIm9wdGlvbnMiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwicmVtb3ZlIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRGcm9tUmVxdWVzdCIsInJlcXVlc3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "pdf-parse":
/*!****************************!*\
  !*** external "pdf-parse" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("pdf-parse");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod","vendor-chunks/@langchain","vendor-chunks/semver","vendor-chunks/zod-to-json-schema","vendor-chunks/langsmith","vendor-chunks/uuid","vendor-chunks/@cfworker","vendor-chunks/retry","vendor-chunks/p-queue","vendor-chunks/js-tiktoken","vendor-chunks/p-timeout","vendor-chunks/p-retry","vendor-chunks/p-finally","vendor-chunks/eventemitter3","vendor-chunks/decamelize","vendor-chunks/camelcase","vendor-chunks/base64-js","vendor-chunks/ansi-styles"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2Fupload%2Froute&page=%2Fapi%2Fdocuments%2Fupload%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2Fupload%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();