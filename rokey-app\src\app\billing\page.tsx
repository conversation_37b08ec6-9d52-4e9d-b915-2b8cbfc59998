'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  CreditCardIcon, 
  CalendarIcon, 
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useSubscription } from '@/hooks/useSubscription';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';
import emailjs from '@emailjs/browser';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface Plan {
  id: string;
  name: string;
  price: number;
  interval: string;
  features: PlanFeature[];
  popular?: boolean;
}

const plans: Plan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    interval: 'forever',
    features: [
      { name: 'Strict fallback routing only', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'No custom roles', included: false },
      { name: 'Configurations', included: true, limit: '1 max' },
      { name: 'API keys per config', included: true, limit: '3 max' },
      { name: 'User-generated API keys', included: true, limit: '3 max' }
    ]
  },
  {
    id: 'starter',
    name: 'Starter',
    price: 19,
    interval: 'month',
    popular: true,
    features: [
      { name: 'All routing strategies', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Custom roles', included: true, limit: 'Up to 3 roles' },
      { name: 'Configurations', included: true, limit: '5 max' },
      { name: 'API keys per config', included: true, limit: '15 max' },
      { name: 'User-generated API keys', included: true, limit: '50 max' },
      { name: 'Browsing tasks', included: true, limit: '15/month' }
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 49,
    interval: 'month',
    features: [
      { name: 'Everything in Starter', included: true },
      { name: 'Unlimited configurations', included: true },
      { name: 'Unlimited API keys per config', included: true },
      { name: 'Unlimited user-generated API keys', included: true },
      { name: 'Knowledge base documents', included: true, limit: '5 documents' },
      { name: 'Priority support', included: true }
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 149,
    interval: 'month',
    features: [
      { name: 'Everything in Professional', included: true },
      { name: 'Unlimited knowledge base documents', included: true },
      { name: 'Advanced semantic caching', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Dedicated support + phone', included: true },
      { name: 'SLA guarantee', included: true }
    ]
  }
];

export default function BillingPage() {
  const router = useRouter();
  const { user, subscriptionStatus, refreshSubscription, createCheckoutSession } = useSubscription();
  const confirmation = useConfirmation();
  
  const [loading, setLoading] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [cancelFeedback, setCancelFeedback] = useState('');

  const currentPlan = plans.find(plan => plan.id === subscriptionStatus?.tier) || plans[0];
  
  // Calculate days until renewal (mock data for now)
  const daysUntilRenewal = subscriptionStatus?.tier !== 'free' ? 
    Math.floor(Math.random() * 30) + 1 : null;

  const handlePlanChange = async (planId: string) => {
    if (planId === currentPlan.id) return;

    const targetPlan = plans.find(p => p.id === planId);
    if (!targetPlan) return;

    const isUpgrade = plans.findIndex(p => p.id === planId) > plans.findIndex(p => p.id === currentPlan.id);

    if (isUpgrade) {
      // For upgrades, go directly to Stripe checkout
      try {
        setLoading(true);
        await createCheckoutSession(planId as any);
      } catch (error: any) {
        console.error('Checkout error:', error);
        toast.error('Failed to start checkout. Please try again.');
        setLoading(false);
      }
    } else {
      // For downgrades, show confirmation
      confirmation.showConfirmation(
        {
          title: 'Downgrade Plan',
          message: `Are you sure you want to downgrade to the ${targetPlan.name} plan? This will take effect at the end of your current billing period.`,
          confirmText: 'Downgrade',
          cancelText: 'Cancel',
          type: 'warning'
        },
        async () => {
          setLoading(true);
          try {
            // TODO: Implement downgrade logic with Stripe
            // For now, we'll simulate the process
            await new Promise(resolve => setTimeout(resolve, 2000));

            toast.success(`Successfully scheduled downgrade to ${targetPlan.name} plan`);
            await refreshSubscription();
          } catch (error: any) {
            console.error('Downgrade error:', error);
            toast.error('Failed to downgrade plan. Please try again.');
          } finally {
            setLoading(false);
          }
        }
      );
    }
  };

  const handleCancelSubscription = async () => {
    if (!cancelReason.trim()) {
      toast.error('Please select a reason for cancellation');
      return;
    }

    setLoading(true);
    try {
      // Send cancellation feedback via EmailJS
      const templateParams = {
        user_email: user?.email || 'Unknown',
        user_name: user?.user_metadata?.first_name || 'User',
        current_plan: currentPlan.name,
        cancel_reason: cancelReason,
        additional_feedback: cancelFeedback,
        cancel_date: new Date().toLocaleDateString()
      };

      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID!,
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID!,
        templateParams,
        process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY!
      );

      // Here you would also cancel the subscription in your payment processor
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('Subscription cancelled successfully. We\'ve sent your feedback to our team.');
      setShowCancelModal(false);
      setCancelReason('');
      setCancelFeedback('');
      await refreshSubscription();
    } catch (error: any) {
      console.error('Cancellation error:', error);
      toast.error('Failed to cancel subscription. Please contact support.');
    } finally {
      setLoading(false);
    }
  };

  const cancelReasons = [
    'Too expensive',
    'Not using enough features',
    'Found a better alternative',
    'Technical issues',
    'Poor customer support',
    'Missing features I need',
    'Temporary financial constraints',
    'Other'
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Billing & Plans</h1>
          <p className="mt-2 text-gray-600">
            Manage your subscription, billing information, and plan features.
          </p>
        </div>

        <div className="space-y-8">
          {/* Current Plan Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCardIcon className="h-5 w-5" />
                Current Plan
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-3">
                    <h3 className="text-2xl font-bold text-gray-900">{currentPlan.name}</h3>
                    {currentPlan.popular && (
                      <Badge className="bg-orange-100 text-orange-800">Popular</Badge>
                    )}
                  </div>
                  <p className="text-gray-600 mt-1">
                    {currentPlan.price === 0 ? 'Free forever' : `$${currentPlan.price}/${currentPlan.interval}`}
                  </p>
                  {daysUntilRenewal && (
                    <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                      <CalendarIcon className="h-4 w-4" />
                      <span>Renews in {daysUntilRenewal} days</span>
                    </div>
                  )}
                </div>
                <div className="text-right">
                  {subscriptionStatus?.tier !== 'free' && (
                    <Button
                      variant="outline"
                      onClick={() => setShowCancelModal(true)}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      Cancel Subscription
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Available Plans */}
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Available Plans</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {plans.map((plan) => (
                <Card key={plan.id} className={`relative ${plan.popular ? 'ring-2 ring-orange-500' : ''}`}>
                  {plan.popular && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-orange-500 text-white">Most Popular</Badge>
                    </div>
                  )}
                  <CardHeader>
                    <CardTitle className="text-center">
                      <div className="text-xl font-bold">{plan.name}</div>
                      <div className="mt-2">
                        <span className="text-3xl font-bold">${plan.price}</span>
                        {plan.price > 0 && <span className="text-gray-500">/{plan.interval}</span>}
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3 mb-6">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-start gap-2">
                          {feature.included ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                          ) : (
                            <XCircleIcon className="h-5 w-5 text-gray-300 flex-shrink-0 mt-0.5" />
                          )}
                          <span className={`text-sm ${feature.included ? 'text-gray-900' : 'text-gray-400'}`}>
                            {feature.name}
                            {feature.limit && (
                              <span className="text-gray-500"> ({feature.limit})</span>
                            )}
                          </span>
                        </li>
                      ))}
                    </ul>
                    
                    {plan.id === currentPlan.id ? (
                      <Button disabled className="w-full">
                        Current Plan
                      </Button>
                    ) : (
                      <Button
                        onClick={() => handlePlanChange(plan.id)}
                        disabled={loading}
                        className="w-full"
                        variant={plan.popular ? "default" : "outline"}
                      >
                        {plans.findIndex(p => p.id === plan.id) > plans.findIndex(p => p.id === currentPlan.id) ? (
                          <>
                            <ArrowUpIcon className="h-4 w-4 mr-2" />
                            Upgrade to {plan.name}
                          </>
                        ) : (
                          <>
                            <ArrowDownIcon className="h-4 w-4 mr-2" />
                            Downgrade to {plan.name}
                          </>
                        )}
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Cancellation Modal */}
        {showCancelModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
              <div className="flex items-center gap-3 mb-4">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
                <h3 className="text-lg font-semibold text-gray-900">Cancel Subscription</h3>
              </div>
              
              <p className="text-gray-600 mb-4">
                We're sorry to see you go! Please help us improve by telling us why you're cancelling.
              </p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason for cancellation *
                  </label>
                  <select
                    value={cancelReason}
                    onChange={(e) => setCancelReason(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option value="">Select a reason...</option>
                    {cancelReasons.map((reason) => (
                      <option key={reason} value={reason}>{reason}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional feedback (optional)
                  </label>
                  <textarea
                    value={cancelFeedback}
                    onChange={(e) => setCancelFeedback(e.target.value)}
                    placeholder="Tell us more about your experience or what we could do better..."
                    rows={3}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <Button
                  variant="outline"
                  onClick={() => setShowCancelModal(false)}
                  className="flex-1"
                >
                  Keep Subscription
                </Button>
                <Button
                  onClick={handleCancelSubscription}
                  disabled={loading || !cancelReason.trim()}
                  className="flex-1 bg-red-600 hover:bg-red-700"
                >
                  {loading ? 'Cancelling...' : 'Cancel Subscription'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={confirmation.isOpen}
          onClose={confirmation.hideConfirmation}
          onConfirm={confirmation.onConfirm}
          title={confirmation.title}
          message={confirmation.message}
          confirmText={confirmation.confirmText}
          cancelText={confirmation.cancelText}
          type={confirmation.type}
          isLoading={confirmation.isLoading}
        />
      </div>
    </div>
  );
}
