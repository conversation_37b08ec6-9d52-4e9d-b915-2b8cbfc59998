"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded, onDocumentDeleted } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation)();\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Replace with server data to avoid duplicates\n                    setDocuments(newDocuments.sort({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            } finally{\n                if (retryCount === 0 || expectedDocumentId) {\n                    setIsRefreshing(false);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename || file.name,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status || 'processing',\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>{\n                // Check if document already exists (shouldn't happen, but safety check)\n                const exists = prev.find((doc)=>doc.id === optimisticDocument.id);\n                if (exists) {\n                    console.log(\"[DocumentUpload] Document \".concat(optimisticDocument.id, \" already exists, updating instead\"));\n                    return prev.map((doc)=>doc.id === optimisticDocument.id ? optimisticDocument : doc);\n                }\n                return [\n                    optimisticDocument,\n                    ...prev\n                ];\n            });\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = (documentId, documentName)=>{\n        confirmation.showConfirmation({\n            title: 'Delete Document',\n            message: 'Are you sure you want to delete \"'.concat(documentName, '\"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.'),\n            confirmText: 'Delete Document',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            // Optimistically remove the document from the list\n            const originalDocuments = documents;\n            setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n            try {\n                const response = await fetch(\"/api/documents/\".concat(documentId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    // Restore the original list on error\n                    setDocuments(originalDocuments);\n                    throw new Error('Failed to delete document');\n                }\n                setSuccess('Document deleted successfully');\n                // Auto-clear success message after 3 seconds\n                setTimeout(()=>setSuccess(null), 3000);\n            } catch (err) {\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                const errorMessage = \"Delete failed: \".concat(err.message);\n                setError(errorMessage);\n                // Auto-clear error message after 8 seconds\n                setTimeout(()=>setError(null), 8000);\n            }\n        });\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 283,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 284,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 285,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800 text-sm font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 301,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Uploaded Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Refreshing...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id, doc.filename),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 365,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: confirmation.isOpen,\n                onClose: confirmation.hideConfirmation,\n                onConfirm: confirmation.onConfirm,\n                title: confirmation.title,\n                message: confirmation.message,\n                confirmText: confirmation.confirmText,\n                cancelText: confirmation.cancelText,\n                type: confirmation.type,\n                isLoading: confirmation.isLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"qy7mnAkmgJ7Ofmiub+gVBhPvEic=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation\n    ];\n});\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0RvY3VtZW50VXBsb2FkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNkQ7QUFDMEM7QUFJN0M7QUFDUTtBQWtCbkQsU0FBU2EsZUFBZSxLQUF3RTtRQUF4RSxFQUFFQyxRQUFRLEVBQUVDLGtCQUFrQixFQUFFQyxpQkFBaUIsRUFBdUIsR0FBeEU7O0lBQ3JDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHakIsK0NBQVFBLENBQWEsRUFBRTtJQUN6RCxNQUFNLENBQUNrQixhQUFhQyxlQUFlLEdBQUduQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNvQixjQUFjQyxnQkFBZ0IsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3NCLGdCQUFnQkMsa0JBQWtCLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN3QixZQUFZQyxjQUFjLEdBQUd6QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUMwQixPQUFPQyxTQUFTLEdBQUczQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDNEIsU0FBU0MsV0FBVyxHQUFHN0IsK0NBQVFBLENBQWdCO0lBQ3RELE1BQU04QixlQUFlNUIsNkNBQU1BLENBQW1CO0lBQzlDLE1BQU02QixlQUFlckIsdUVBQWVBO0lBRXBDLHlEQUF5RDtJQUN6RCxNQUFNc0IsZ0JBQWdCL0Isa0RBQVdBO3FEQUFDO2dCQUFPZ0MsOEVBQWEsR0FBR0M7WUFDdkQsSUFBSSxDQUFDckIsVUFBVTtZQUVmLElBQUlvQixlQUFlLEdBQUc7Z0JBQ3BCWixnQkFBZ0I7WUFDbEI7WUFFQSxJQUFJO2dCQUNGLE1BQU1jLFdBQVcsTUFBTUMsTUFBTSxnQ0FBeUMsT0FBVHZCO2dCQUM3RCxJQUFJc0IsU0FBU0UsRUFBRSxFQUFFO29CQUNmLE1BQU1DLE9BQU8sTUFBTUgsU0FBU0ksSUFBSTtvQkFDaEMsTUFBTUMsZUFBZUYsS0FBS3RCLFNBQVMsSUFBSSxFQUFFO29CQUV6QyxxRUFBcUU7b0JBQ3JFLElBQUlrQixzQkFBc0JELGFBQWEsR0FBRzt3QkFDeEMsTUFBTVEsZ0JBQWdCRCxhQUFhRSxJQUFJO3VGQUFDLENBQUNDLE1BQWtCQSxJQUFJQyxFQUFFLEtBQUtWOzt3QkFDdEUsSUFBSSxDQUFDTyxlQUFlOzRCQUNsQkksUUFBUUMsR0FBRyxDQUFDLDZCQUEwRSxPQUE3Q1osb0JBQW1CLDRCQUFpRCxPQUF2QixDQUFDRCxhQUFhLEtBQUssS0FBSTs0QkFDN0djOzZFQUFXO29DQUNUZixjQUFjQyxhQUFhLEdBQUdDO2dDQUNoQzs0RUFBRyxDQUFDRCxhQUFhLEtBQUssTUFBTSx5QkFBeUI7NEJBQ3JEO3dCQUNGO29CQUNGO29CQUVBLCtDQUErQztvQkFDL0NoQixhQUFhdUIsYUFBYVEsSUFBSTtxRUFBQyxDQUFDQyxHQUFHQyxJQUFNLElBQUlDLEtBQUtELEVBQUVFLFVBQVUsRUFBRUMsT0FBTyxLQUFLLElBQUlGLEtBQUtGLEVBQUVHLFVBQVUsRUFBRUMsT0FBTzs7Z0JBQzVHO1lBQ0YsRUFBRSxPQUFPQyxLQUFLO2dCQUNaVCxRQUFRbkIsS0FBSyxDQUFDLDZCQUE2QjRCO2dCQUUzQyxvREFBb0Q7Z0JBQ3BELElBQUlyQixhQUFhLEdBQUc7b0JBQ2xCYztxRUFBVzs0QkFDVGYsY0FBY0MsYUFBYSxHQUFHQzt3QkFDaEM7b0VBQUc7Z0JBQ0w7WUFDRixTQUFVO2dCQUNSLElBQUlELGVBQWUsS0FBS0Msb0JBQW9CO29CQUMxQ2IsZ0JBQWdCO2dCQUNsQjtZQUNGO1FBQ0Y7b0RBQUc7UUFBQ1I7S0FBUztJQUViLHVDQUF1QztJQUN2Q2Qsc0RBQWU7b0NBQUM7WUFDZGlDO1FBQ0Y7bUNBQUc7UUFBQ0E7S0FBYztJQUVsQixxQkFBcUI7SUFDckIsTUFBTXdCLG1CQUFtQixPQUFPQztRQUM5QixJQUFJLENBQUM1QyxVQUFVO1lBQ2JjLFNBQVM7WUFDVDtRQUNGO1FBRUEsTUFBTStCLE9BQU9ELEtBQUssQ0FBQyxFQUFFO1FBQ3JCLElBQUksQ0FBQ0MsTUFBTTtRQUVYLHFCQUFxQjtRQUNyQixNQUFNQyxlQUFlO1lBQ25CO1lBQ0E7WUFDQTtTQUNEO1FBRUQsSUFBSSxDQUFDQSxhQUFhQyxRQUFRLENBQUNGLEtBQUtHLElBQUksR0FBRztZQUNyQ2xDLFNBQVM7WUFDVDtRQUNGO1FBRUEsZ0NBQWdDO1FBQ2hDLElBQUkrQixLQUFLSSxJQUFJLEdBQUcsS0FBSyxPQUFPLE1BQU07WUFDaENuQyxTQUFTO1lBQ1Q7UUFDRjtRQUVBUixlQUFlO1FBQ2ZRLFNBQVM7UUFDVEUsV0FBVztRQUNYTixrQkFBa0I7UUFFbEIsSUFBSTtZQUNGLE1BQU13QyxXQUFXLElBQUlDO1lBQ3JCRCxTQUFTRSxNQUFNLENBQUMsUUFBUVA7WUFDeEJLLFNBQVNFLE1BQU0sQ0FBQyxZQUFZcEQ7WUFFNUIsa0NBQWtDO1lBQ2xDLE1BQU1xRCxtQkFBbUJDLFlBQVk7Z0JBQ25DNUMsa0JBQWtCNkMsQ0FBQUEsT0FBUUMsS0FBS0MsR0FBRyxDQUFDRixPQUFPLElBQUk7WUFDaEQsR0FBRztZQUVILE1BQU1qQyxXQUFXLE1BQU1DLE1BQU0seUJBQXlCO2dCQUNwRG1DLFFBQVE7Z0JBQ1JDLE1BQU1UO1lBQ1I7WUFFQVUsY0FBY1A7WUFDZDNDLGtCQUFrQjtZQUVsQixJQUFJLENBQUNZLFNBQVNFLEVBQUUsRUFBRTtnQkFDaEIsTUFBTXFDLFlBQVksTUFBTXZDLFNBQVNJLElBQUk7Z0JBQ3JDLE1BQU0sSUFBSW9DLE1BQU1ELFVBQVVoRCxLQUFLLElBQUk7WUFDckM7WUFFQSxNQUFNa0QsU0FBUyxNQUFNekMsU0FBU0ksSUFBSTtZQUNsQ00sUUFBUUMsR0FBRyxDQUFFLHVDQUFzQzhCO1lBQ25EL0MsV0FBVyxLQUFvRCtDLE9BQS9DbEIsS0FBS21CLElBQUksRUFBQyx1Q0FBa0UsT0FBN0JELE9BQU9FLFFBQVEsQ0FBQ0MsWUFBWSxFQUFDO1lBRTVGLDBEQUEwRDtZQUMxRCxNQUFNQyxxQkFBK0I7Z0JBQ25DcEMsSUFBSWdDLE9BQU9FLFFBQVEsQ0FBQ2xDLEVBQUU7Z0JBQ3RCcUMsVUFBVUwsT0FBT0UsUUFBUSxDQUFDRyxRQUFRLElBQUl2QixLQUFLbUIsSUFBSTtnQkFDL0NLLFdBQVd4QixLQUFLRyxJQUFJO2dCQUNwQnNCLFdBQVd6QixLQUFLSSxJQUFJO2dCQUNwQnNCLFFBQVEsT0FBUU4sUUFBUSxDQUFDTSxNQUFNLElBQThDO2dCQUM3RUMsY0FBY1QsT0FBT0UsUUFBUSxDQUFDUSxnQkFBZ0IsSUFBSTtnQkFDbERsQyxZQUFZLElBQUlELE9BQU9vQyxXQUFXO1lBQ3BDO1lBRUExQyxRQUFRQyxHQUFHLENBQUUsZ0RBQStDa0M7WUFDNUQvRCxhQUFhbUQsQ0FBQUE7Z0JBQ1gsd0VBQXdFO2dCQUN4RSxNQUFNb0IsU0FBU3BCLEtBQUsxQixJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLEVBQUUsS0FBS29DLG1CQUFtQnBDLEVBQUU7Z0JBQ2hFLElBQUk0QyxRQUFRO29CQUNWM0MsUUFBUUMsR0FBRyxDQUFDLDZCQUFtRCxPQUF0QmtDLG1CQUFtQnBDLEVBQUUsRUFBQztvQkFDL0QsT0FBT3dCLEtBQUtxQixHQUFHLENBQUM5QyxDQUFBQSxNQUFPQSxJQUFJQyxFQUFFLEtBQUtvQyxtQkFBbUJwQyxFQUFFLEdBQUdvQyxxQkFBcUJyQztnQkFDakY7Z0JBQ0EsT0FBTztvQkFBQ3FDO3VCQUF1Qlo7aUJBQUs7WUFDdEM7WUFFQSxvRkFBb0Y7WUFDcEZyQixXQUFXO2dCQUNURixRQUFRQyxHQUFHLENBQUMsaUVBQW9GLE9BQW5COEIsT0FBT0UsUUFBUSxDQUFDbEMsRUFBRTtnQkFDL0YsTUFBTVosY0FBYyxHQUFHNEMsT0FBT0UsUUFBUSxDQUFDbEMsRUFBRTtZQUMzQyxHQUFHO1lBRUgsNEJBQTRCO1lBQzVCOUIsK0JBQUFBLHlDQUFBQTtRQUVGLEVBQUUsT0FBT3dDLEtBQVU7WUFDakIsTUFBTW9DLGVBQWUsa0JBQThCLE9BQVpwQyxJQUFJcUMsT0FBTztZQUNsRGhFLFNBQVMrRDtZQUVULDJDQUEyQztZQUMzQzNDLFdBQVcsSUFBTXBCLFNBQVMsT0FBTztRQUNuQyxTQUFVO1lBQ1JSLGVBQWU7WUFDZkksa0JBQWtCO1lBRWxCLG1CQUFtQjtZQUNuQixJQUFJTyxhQUFhOEQsT0FBTyxFQUFFO2dCQUN4QjlELGFBQWE4RCxPQUFPLENBQUNDLEtBQUssR0FBRztZQUMvQjtZQUVBLDZDQUE2QztZQUM3QyxJQUFJakUsU0FBUztnQkFDWG1CLFdBQVcsSUFBTWxCLFdBQVcsT0FBTztZQUNyQztRQUNGO0lBQ0Y7SUFFQSx1QkFBdUI7SUFDdkIsTUFBTWlFLGFBQWE3RixrREFBV0E7a0RBQUMsQ0FBQzhGO1lBQzlCQSxFQUFFQyxjQUFjO1lBQ2hCRCxFQUFFRSxlQUFlO1lBQ2pCLElBQUlGLEVBQUVsQyxJQUFJLEtBQUssZUFBZWtDLEVBQUVsQyxJQUFJLEtBQUssWUFBWTtnQkFDbkRwQyxjQUFjO1lBQ2hCLE9BQU8sSUFBSXNFLEVBQUVsQyxJQUFJLEtBQUssYUFBYTtnQkFDakNwQyxjQUFjO1lBQ2hCO1FBQ0Y7aURBQUcsRUFBRTtJQUVMLE1BQU15RSxhQUFhakcsa0RBQVdBO2tEQUFDLENBQUM4RjtZQUM5QkEsRUFBRUMsY0FBYztZQUNoQkQsRUFBRUUsZUFBZTtZQUNqQnhFLGNBQWM7WUFFZCxJQUFJc0UsRUFBRUksWUFBWSxDQUFDMUMsS0FBSyxJQUFJc0MsRUFBRUksWUFBWSxDQUFDMUMsS0FBSyxDQUFDLEVBQUUsRUFBRTtnQkFDbkRELGlCQUFpQnVDLEVBQUVJLFlBQVksQ0FBQzFDLEtBQUs7WUFDdkM7UUFDRjtpREFBRztRQUFDNUM7S0FBUztJQUViLDJCQUEyQjtJQUMzQixNQUFNdUYsb0JBQW9CLENBQUNMO1FBQ3pCLElBQUlBLEVBQUVNLE1BQU0sQ0FBQzVDLEtBQUssSUFBSXNDLEVBQUVNLE1BQU0sQ0FBQzVDLEtBQUssQ0FBQyxFQUFFLEVBQUU7WUFDdkNELGlCQUFpQnVDLEVBQUVNLE1BQU0sQ0FBQzVDLEtBQUs7UUFDakM7SUFDRjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNNkMsdUJBQXVCLENBQUNDLFlBQW9CQztRQUNoRHpFLGFBQWEwRSxnQkFBZ0IsQ0FDM0I7WUFDRUMsT0FBTztZQUNQZixTQUFTLG9DQUFpRCxPQUFiYSxjQUFhO1lBQzFERyxhQUFhO1lBQ2JDLFlBQVk7WUFDWi9DLE1BQU07UUFDUixHQUNBO1lBQ0UsbURBQW1EO1lBQ25ELE1BQU1nRCxvQkFBb0I3RjtZQUMxQkMsYUFBYW1ELENBQUFBLE9BQVFBLEtBQUswQyxNQUFNLENBQUNuRSxDQUFBQSxNQUFPQSxJQUFJQyxFQUFFLEtBQUsyRDtZQUVuRCxJQUFJO2dCQUNGLE1BQU1wRSxXQUFXLE1BQU1DLE1BQU0sa0JBQTZCLE9BQVhtRSxhQUFjO29CQUMzRGhDLFFBQVE7Z0JBQ1Y7Z0JBRUEsSUFBSSxDQUFDcEMsU0FBU0UsRUFBRSxFQUFFO29CQUNoQixxQ0FBcUM7b0JBQ3JDcEIsYUFBYTRGO29CQUNiLE1BQU0sSUFBSWxDLE1BQU07Z0JBQ2xCO2dCQUVBOUMsV0FBVztnQkFFWCw2Q0FBNkM7Z0JBQzdDa0IsV0FBVyxJQUFNbEIsV0FBVyxPQUFPO1lBQ3JDLEVBQUUsT0FBT3lCLEtBQVU7Z0JBQ2pCLHFDQUFxQztnQkFDckNyQyxhQUFhNEY7Z0JBQ2IsTUFBTW5CLGVBQWUsa0JBQThCLE9BQVpwQyxJQUFJcUMsT0FBTztnQkFDbERoRSxTQUFTK0Q7Z0JBRVQsMkNBQTJDO2dCQUMzQzNDLFdBQVcsSUFBTXBCLFNBQVMsT0FBTztZQUNuQztRQUNGO0lBRUo7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTW9GLGlCQUFpQixDQUFDQztRQUN0QixJQUFJQSxVQUFVLEdBQUcsT0FBTztRQUN4QixNQUFNQyxJQUFJO1FBQ1YsTUFBTUMsUUFBUTtZQUFDO1lBQVM7WUFBTTtZQUFNO1NBQUs7UUFDekMsTUFBTUMsSUFBSTlDLEtBQUsrQyxLQUFLLENBQUMvQyxLQUFLdkIsR0FBRyxDQUFDa0UsU0FBUzNDLEtBQUt2QixHQUFHLENBQUNtRTtRQUNoRCxPQUFPSSxXQUFXLENBQUNMLFFBQVEzQyxLQUFLaUQsR0FBRyxDQUFDTCxHQUFHRSxFQUFDLEVBQUdJLE9BQU8sQ0FBQyxNQUFNLE1BQU1MLEtBQUssQ0FBQ0MsRUFBRTtJQUN6RTtJQUVBLGdCQUFnQjtJQUNoQixNQUFNSyxjQUFjLENBQUNDO1FBQ25CLElBQUlBLFNBQVM3RCxRQUFRLENBQUMsUUFBUSxxQkFBTyw4REFBQ3BELGtJQUFRQTtZQUFDa0gsV0FBVTs7Ozs7O1FBQ3pELElBQUlELFNBQVM3RCxRQUFRLENBQUMsU0FBUyxxQkFBTyw4REFBQ3BELGtJQUFRQTtZQUFDa0gsV0FBVTs7Ozs7O1FBQzFELHFCQUFPLDhEQUFDdEgsa0lBQUlBO1lBQUNzSCxXQUFVOzs7Ozs7SUFDekI7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7WUFFWmhHLHVCQUNDLDhEQUFDaUc7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ25ILGtJQUFXQTs0QkFBQ21ILFdBQVU7Ozs7OztzQ0FDdkIsOERBQUNFOzRCQUFFRixXQUFVO3NDQUFvQ2hHOzs7Ozs7Ozs7Ozs7Ozs7OztZQUt0REUseUJBQ0MsOERBQUMrRjtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDcEgsa0lBQVdBOzRCQUFDb0gsV0FBVTs7Ozs7O3NDQUN2Qiw4REFBQ0U7NEJBQUVGLFdBQVU7c0NBQXNDOUY7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU16RCw4REFBQytGO2dCQUNDRCxXQUFXLHFHQUlQLE9BSEZsRyxhQUNJLHVEQUNBLDhGQUNMLEtBQWtFLE9BQS9ELENBQUNYLFdBQVcsa0NBQWtDO2dCQUNsRGdILGFBQWEvQjtnQkFDYmdDLGFBQWFoQztnQkFDYmlDLFlBQVlqQztnQkFDWmtDLFFBQVE5QjtnQkFDUitCLFNBQVM7d0JBQWtCbkc7MkJBQVpqQixjQUFZaUIsd0JBQUFBLGFBQWE4RCxPQUFPLGNBQXBCOUQsNENBQUFBLHNCQUFzQm9HLEtBQUs7OztrQ0FFdEQsOERBQUNDO3dCQUNDQyxLQUFLdEc7d0JBQ0wrQixNQUFLO3dCQUNMNkQsV0FBVTt3QkFDVlcsUUFBTzt3QkFDUEMsVUFBVWxDO3dCQUNWbUMsVUFBVSxDQUFDMUgsWUFBWUs7Ozs7OztvQkFHeEJBLDRCQUNDLDhEQUFDeUc7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDakgsa0lBQU9BO2dDQUFDaUgsV0FBVTs7Ozs7OzBDQUNuQiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBb0M7Ozs7OztrREFDakQsOERBQUNDO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDQzs0Q0FDQ0QsV0FBVTs0Q0FDVmMsT0FBTztnREFBRUMsT0FBTyxHQUFrQixPQUFmbkgsZ0JBQWU7NENBQUc7Ozs7Ozs7Ozs7O2tEQUd6Qyw4REFBQ3NHO3dDQUFFRixXQUFVOzs0Q0FBeUJwRzs0Q0FBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZDQUl6RCw4REFBQ3FHO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ3ZILGtJQUFNQTtnQ0FBQ3VILFdBQVU7Ozs7OzswQ0FDbEIsOERBQUNDOztrREFDQyw4REFBQ0M7d0NBQUVGLFdBQVU7a0RBQ1Y3RyxXQUFXLCtCQUErQjs7Ozs7O2tEQUU3Qyw4REFBQytHO3dDQUFFRixXQUFVO2tEQUE2Qjs7Ozs7O2tEQUcxQyw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFTakQxRyxVQUFVMEgsTUFBTSxHQUFHLG1CQUNsQiw4REFBQ2Y7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNpQjtnQ0FBR2pCLFdBQVU7MENBQXNDOzs7Ozs7NEJBQ25EdEcsOEJBQ0MsOERBQUN1RztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNqSCxrSUFBT0E7d0NBQUNpSCxXQUFVOzs7Ozs7a0RBQ25CLDhEQUFDa0I7a0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJWiw4REFBQ2pCO3dCQUFJRCxXQUFVO2tDQUNaMUcsVUFBVXlFLEdBQUcsQ0FBQyxDQUFDOUMsb0JBQ2QsOERBQUNnRjtnQ0FFQ0QsV0FBVTs7a0RBRVYsOERBQUNDO3dDQUFJRCxXQUFVOzs0Q0FDWkYsWUFBWTdFLElBQUl1QyxTQUFTOzBEQUMxQiw4REFBQ3lDOztrRUFDQyw4REFBQ0M7d0RBQUVGLFdBQVU7a0VBQTZCL0UsSUFBSXNDLFFBQVE7Ozs7OztrRUFDdEQsOERBQUMyQzt3REFBRUYsV0FBVTs7NERBQ1ZYLGVBQWVwRSxJQUFJd0MsU0FBUzs0REFBRTs0REFBSXhDLElBQUkwQyxZQUFZOzREQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUsxRCw4REFBQ3NDO3dDQUFJRCxXQUFVOzswREFDYiw4REFBQ0M7Z0RBQUlELFdBQVU7O29EQUNaL0UsSUFBSXlDLE1BQU0sS0FBSyw2QkFDZCw4REFBQzlFLGtJQUFXQTt3REFBQ29ILFdBQVU7Ozs7OztvREFFeEIvRSxJQUFJeUMsTUFBTSxLQUFLLDhCQUNkLDhEQUFDM0Usa0lBQU9BO3dEQUFDaUgsV0FBVTs7Ozs7O29EQUVwQi9FLElBQUl5QyxNQUFNLEtBQUssMEJBQ2QsOERBQUM3RSxrSUFBV0E7d0RBQUNtSCxXQUFVOzs7Ozs7a0VBRXpCLDhEQUFDa0I7d0RBQUtsQixXQUFXLHVCQUloQixPQUhDL0UsSUFBSXlDLE1BQU0sS0FBSyxjQUFjLG1CQUM3QnpDLElBQUl5QyxNQUFNLEtBQUssZUFBZSxvQkFDOUI7a0VBRUN6QyxJQUFJeUMsTUFBTSxLQUFLLGNBQWMsVUFDN0J6QyxJQUFJeUMsTUFBTSxLQUFLLGVBQWUsZUFDOUI7Ozs7Ozs7Ozs7OzswREFJTCw4REFBQ3lEO2dEQUNDWixTQUFTLElBQU0zQixxQkFBcUIzRCxJQUFJQyxFQUFFLEVBQUVELElBQUlzQyxRQUFRO2dEQUN4RHlDLFdBQVU7Z0RBQ1ZoQixPQUFNOzBEQUVOLDRFQUFDckcsbUlBQUNBO29EQUFDcUgsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQXhDWi9FLElBQUlDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBa0RyQiw4REFBQ2pDLHdFQUFpQkE7Z0JBQ2hCbUksUUFBUS9HLGFBQWErRyxNQUFNO2dCQUMzQkMsU0FBU2hILGFBQWFpSCxnQkFBZ0I7Z0JBQ3RDQyxXQUFXbEgsYUFBYWtILFNBQVM7Z0JBQ2pDdkMsT0FBTzNFLGFBQWEyRSxLQUFLO2dCQUN6QmYsU0FBUzVELGFBQWE0RCxPQUFPO2dCQUM3QmdCLGFBQWE1RSxhQUFhNEUsV0FBVztnQkFDckNDLFlBQVk3RSxhQUFhNkUsVUFBVTtnQkFDbkMvQyxNQUFNOUIsYUFBYThCLElBQUk7Z0JBQ3ZCcUYsV0FBV25ILGFBQWFtSCxTQUFTOzs7Ozs7Ozs7Ozs7QUFJekM7R0E5WndCdEk7O1FBU0RGLG1FQUFlQTs7O0tBVGRFIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcRG9jdW1lbnRVcGxvYWQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVXBsb2FkLCBGaWxlLCBYLCBDaGVja0NpcmNsZSwgQWxlcnRDaXJjbGUsIEZpbGVUZXh0LCBGaWxlSW1hZ2UsIExvYWRlcjIgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlU3Vic2NyaXB0aW9uIH0gZnJvbSAnQC9ob29rcy91c2VTdWJzY3JpcHRpb24nO1xuaW1wb3J0IHsgZ2V0VGllckNvbmZpZywgU3Vic2NyaXB0aW9uVGllciB9IGZyb20gJ0AvbGliL3N0cmlwZS1jbGllbnQnO1xuaW1wb3J0IHsgTGltaXRJbmRpY2F0b3IgfSBmcm9tICdAL2NvbXBvbmVudHMvVGllckVuZm9yY2VtZW50JztcbmltcG9ydCB7IHVzZUNvbmZpcm1hdGlvbiB9IGZyb20gJ0AvaG9va3MvdXNlQ29uZmlybWF0aW9uJztcbmltcG9ydCBDb25maXJtYXRpb25Nb2RhbCBmcm9tICdAL2NvbXBvbmVudHMvdWkvQ29uZmlybWF0aW9uTW9kYWwnO1xuXG5pbnRlcmZhY2UgRG9jdW1lbnQge1xuICBpZDogc3RyaW5nO1xuICBmaWxlbmFtZTogc3RyaW5nO1xuICBmaWxlX3R5cGU6IHN0cmluZztcbiAgZmlsZV9zaXplOiBudW1iZXI7XG4gIHN0YXR1czogJ3Byb2Nlc3NpbmcnIHwgJ2NvbXBsZXRlZCcgfCAnZmFpbGVkJztcbiAgY2h1bmtzX2NvdW50OiBudW1iZXI7XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIERvY3VtZW50VXBsb2FkUHJvcHMge1xuICBjb25maWdJZDogc3RyaW5nO1xuICBvbkRvY3VtZW50VXBsb2FkZWQ/OiAoKSA9PiB2b2lkO1xuICBvbkRvY3VtZW50RGVsZXRlZD86ICgpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50VXBsb2FkKHsgY29uZmlnSWQsIG9uRG9jdW1lbnRVcGxvYWRlZCwgb25Eb2N1bWVudERlbGV0ZWQgfTogRG9jdW1lbnRVcGxvYWRQcm9wcykge1xuICBjb25zdCBbZG9jdW1lbnRzLCBzZXREb2N1bWVudHNdID0gdXNlU3RhdGU8RG9jdW1lbnRbXT4oW10pO1xuICBjb25zdCBbaXNVcGxvYWRpbmcsIHNldElzVXBsb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzUmVmcmVzaGluZywgc2V0SXNSZWZyZXNoaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3VwbG9hZFByb2dyZXNzLCBzZXRVcGxvYWRQcm9ncmVzc10gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW2RyYWdBY3RpdmUsIHNldERyYWdBY3RpdmVdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc3VjY2Vzcywgc2V0U3VjY2Vzc10gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgZmlsZUlucHV0UmVmID0gdXNlUmVmPEhUTUxJbnB1dEVsZW1lbnQ+KG51bGwpO1xuICBjb25zdCBjb25maXJtYXRpb24gPSB1c2VDb25maXJtYXRpb24oKTtcblxuICAvLyBMb2FkIGRvY3VtZW50cyBmb3IgdGhlIGN1cnJlbnQgY29uZmlnIHdpdGggcmV0cnkgbG9naWNcbiAgY29uc3QgbG9hZERvY3VtZW50cyA9IHVzZUNhbGxiYWNrKGFzeW5jIChyZXRyeUNvdW50ID0gMCwgZXhwZWN0ZWREb2N1bWVudElkPzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maWdJZCkgcmV0dXJuO1xuXG4gICAgaWYgKHJldHJ5Q291bnQgPT09IDApIHtcbiAgICAgIHNldElzUmVmcmVzaGluZyh0cnVlKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9kb2N1bWVudHMvbGlzdD9jb25maWdJZD0ke2NvbmZpZ0lkfWApO1xuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGNvbnN0IG5ld0RvY3VtZW50cyA9IGRhdGEuZG9jdW1lbnRzIHx8IFtdO1xuXG4gICAgICAgIC8vIElmIHdlJ3JlIGxvb2tpbmcgZm9yIGEgc3BlY2lmaWMgZG9jdW1lbnQgYW5kIGl0J3Mgbm90IGZvdW5kLCByZXRyeVxuICAgICAgICBpZiAoZXhwZWN0ZWREb2N1bWVudElkICYmIHJldHJ5Q291bnQgPCAzKSB7XG4gICAgICAgICAgY29uc3QgZm91bmREb2N1bWVudCA9IG5ld0RvY3VtZW50cy5maW5kKChkb2M6IERvY3VtZW50KSA9PiBkb2MuaWQgPT09IGV4cGVjdGVkRG9jdW1lbnRJZCk7XG4gICAgICAgICAgaWYgKCFmb3VuZERvY3VtZW50KSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgW0RvY3VtZW50VXBsb2FkXSBEb2N1bWVudCAke2V4cGVjdGVkRG9jdW1lbnRJZH0gbm90IGZvdW5kLCByZXRyeWluZyBpbiAkeyhyZXRyeUNvdW50ICsgMSkgKiA1MDB9bXMuLi5gKTtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICBsb2FkRG9jdW1lbnRzKHJldHJ5Q291bnQgKyAxLCBleHBlY3RlZERvY3VtZW50SWQpO1xuICAgICAgICAgICAgfSwgKHJldHJ5Q291bnQgKyAxKSAqIDUwMCk7IC8vIDUwMG1zLCAxcywgMS41cyBkZWxheXNcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBSZXBsYWNlIHdpdGggc2VydmVyIGRhdGEgdG8gYXZvaWQgZHVwbGljYXRlc1xuICAgICAgICBzZXREb2N1bWVudHMobmV3RG9jdW1lbnRzLnNvcnQoKGEsIGIpID0+IG5ldyBEYXRlKGIuY3JlYXRlZF9hdCkuZ2V0VGltZSgpIC0gbmV3IERhdGUoYS5jcmVhdGVkX2F0KS5nZXRUaW1lKCkpKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGRvY3VtZW50czonLCBlcnIpO1xuXG4gICAgICAvLyBSZXRyeSBvbiBlcnJvciBpZiB3ZSBoYXZlbid0IGV4Y2VlZGVkIHJldHJ5IGNvdW50XG4gICAgICBpZiAocmV0cnlDb3VudCA8IDIpIHtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgbG9hZERvY3VtZW50cyhyZXRyeUNvdW50ICsgMSwgZXhwZWN0ZWREb2N1bWVudElkKTtcbiAgICAgICAgfSwgMTAwMCk7XG4gICAgICB9XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIGlmIChyZXRyeUNvdW50ID09PSAwIHx8IGV4cGVjdGVkRG9jdW1lbnRJZCkge1xuICAgICAgICBzZXRJc1JlZnJlc2hpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW2NvbmZpZ0lkXSk7XG5cbiAgLy8gTG9hZCBkb2N1bWVudHMgd2hlbiBjb25maWdJZCBjaGFuZ2VzXG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZERvY3VtZW50cygpO1xuICB9LCBbbG9hZERvY3VtZW50c10pO1xuXG4gIC8vIEhhbmRsZSBmaWxlIHVwbG9hZFxuICBjb25zdCBoYW5kbGVGaWxlVXBsb2FkID0gYXN5bmMgKGZpbGVzOiBGaWxlTGlzdCkgPT4ge1xuICAgIGlmICghY29uZmlnSWQpIHtcbiAgICAgIHNldEVycm9yKCdQbGVhc2Ugc2VsZWN0IGFuIEFQSSBjb25maWd1cmF0aW9uIGZpcnN0Jyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgZmlsZSA9IGZpbGVzWzBdO1xuICAgIGlmICghZmlsZSkgcmV0dXJuO1xuXG4gICAgLy8gVmFsaWRhdGUgZmlsZSB0eXBlXG4gICAgY29uc3QgYWxsb3dlZFR5cGVzID0gW1xuICAgICAgJ2FwcGxpY2F0aW9uL3BkZicsXG4gICAgICAndGV4dC9wbGFpbicsXG4gICAgICAndGV4dC9tYXJrZG93bidcbiAgICBdO1xuXG4gICAgaWYgKCFhbGxvd2VkVHlwZXMuaW5jbHVkZXMoZmlsZS50eXBlKSkge1xuICAgICAgc2V0RXJyb3IoJ1BsZWFzZSB1cGxvYWQgUERGLCBUWFQsIG9yIE1EIGZpbGVzIG9ubHknKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBWYWxpZGF0ZSBmaWxlIHNpemUgKDEwTUIgbWF4KVxuICAgIGlmIChmaWxlLnNpemUgPiAxMCAqIDEwMjQgKiAxMDI0KSB7XG4gICAgICBzZXRFcnJvcignRmlsZSBzaXplIG11c3QgYmUgbGVzcyB0aGFuIDEwTUInKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc1VwbG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICBzZXRTdWNjZXNzKG51bGwpO1xuICAgIHNldFVwbG9hZFByb2dyZXNzKDApO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XG4gICAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKTtcbiAgICAgIGZvcm1EYXRhLmFwcGVuZCgnY29uZmlnSWQnLCBjb25maWdJZCk7XG5cbiAgICAgIC8vIFNpbXVsYXRlIHByb2dyZXNzIGZvciBiZXR0ZXIgVVhcbiAgICAgIGNvbnN0IHByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICAgIHNldFVwbG9hZFByb2dyZXNzKHByZXYgPT4gTWF0aC5taW4ocHJldiArIDEwLCA5MCkpO1xuICAgICAgfSwgMjAwKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9kb2N1bWVudHMvdXBsb2FkJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogZm9ybURhdGEsXG4gICAgICB9KTtcblxuICAgICAgY2xlYXJJbnRlcnZhbChwcm9ncmVzc0ludGVydmFsKTtcbiAgICAgIHNldFVwbG9hZFByb2dyZXNzKDEwMCk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLmVycm9yIHx8ICdVcGxvYWQgZmFpbGVkJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbRG9jdW1lbnRVcGxvYWRdIFVwbG9hZCBzdWNjZXNzZnVsOmAsIHJlc3VsdCk7XG4gICAgICBzZXRTdWNjZXNzKGDinKggJHtmaWxlLm5hbWV9IHVwbG9hZGVkIHN1Y2Nlc3NmdWxseSEgUHJvY2Vzc2luZyAke3Jlc3VsdC5kb2N1bWVudC5jaHVua3NfdG90YWx9IGNodW5rcy5gKTtcblxuICAgICAgLy8gT3B0aW1pc3RpY2FsbHkgYWRkIHRoZSBkb2N1bWVudCB0byB0aGUgbGlzdCBpbW1lZGlhdGVseVxuICAgICAgY29uc3Qgb3B0aW1pc3RpY0RvY3VtZW50OiBEb2N1bWVudCA9IHtcbiAgICAgICAgaWQ6IHJlc3VsdC5kb2N1bWVudC5pZCxcbiAgICAgICAgZmlsZW5hbWU6IHJlc3VsdC5kb2N1bWVudC5maWxlbmFtZSB8fCBmaWxlLm5hbWUsXG4gICAgICAgIGZpbGVfdHlwZTogZmlsZS50eXBlLFxuICAgICAgICBmaWxlX3NpemU6IGZpbGUuc2l6ZSxcbiAgICAgICAgc3RhdHVzOiAocmVzdWx0LmRvY3VtZW50LnN0YXR1cyBhcyAncHJvY2Vzc2luZycgfCAnY29tcGxldGVkJyB8ICdmYWlsZWQnKSB8fCAncHJvY2Vzc2luZycsXG4gICAgICAgIGNodW5rc19jb3VudDogcmVzdWx0LmRvY3VtZW50LmNodW5rc19wcm9jZXNzZWQgfHwgMCxcbiAgICAgICAgY3JlYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZyhgW0RvY3VtZW50VXBsb2FkXSBBZGRpbmcgb3B0aW1pc3RpYyBkb2N1bWVudDpgLCBvcHRpbWlzdGljRG9jdW1lbnQpO1xuICAgICAgc2V0RG9jdW1lbnRzKHByZXYgPT4ge1xuICAgICAgICAvLyBDaGVjayBpZiBkb2N1bWVudCBhbHJlYWR5IGV4aXN0cyAoc2hvdWxkbid0IGhhcHBlbiwgYnV0IHNhZmV0eSBjaGVjaylcbiAgICAgICAgY29uc3QgZXhpc3RzID0gcHJldi5maW5kKGRvYyA9PiBkb2MuaWQgPT09IG9wdGltaXN0aWNEb2N1bWVudC5pZCk7XG4gICAgICAgIGlmIChleGlzdHMpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZyhgW0RvY3VtZW50VXBsb2FkXSBEb2N1bWVudCAke29wdGltaXN0aWNEb2N1bWVudC5pZH0gYWxyZWFkeSBleGlzdHMsIHVwZGF0aW5nIGluc3RlYWRgKTtcbiAgICAgICAgICByZXR1cm4gcHJldi5tYXAoZG9jID0+IGRvYy5pZCA9PT0gb3B0aW1pc3RpY0RvY3VtZW50LmlkID8gb3B0aW1pc3RpY0RvY3VtZW50IDogZG9jKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gW29wdGltaXN0aWNEb2N1bWVudCwgLi4ucHJldl07XG4gICAgICB9KTtcblxuICAgICAgLy8gU21hbGwgZGVsYXkgdG8gYWxsb3cgZGF0YWJhc2UgdHJhbnNhY3Rpb24gdG8gY29tbWl0LCB0aGVuIHJlbG9hZCB3aXRoIHJldHJ5IGxvZ2ljXG4gICAgICBzZXRUaW1lb3V0KGFzeW5jICgpID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coYFtEb2N1bWVudFVwbG9hZF0gU3RhcnRpbmcgZG9jdW1lbnQgbGlzdCByZWZyZXNoIGZvciBkb2N1bWVudDogJHtyZXN1bHQuZG9jdW1lbnQuaWR9YCk7XG4gICAgICAgIGF3YWl0IGxvYWREb2N1bWVudHMoMCwgcmVzdWx0LmRvY3VtZW50LmlkKTtcbiAgICAgIH0sIDIwMCk7XG5cbiAgICAgIC8vIENhbGwgY2FsbGJhY2sgaWYgcHJvdmlkZWRcbiAgICAgIG9uRG9jdW1lbnRVcGxvYWRlZD8uKCk7XG5cbiAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gYFVwbG9hZCBmYWlsZWQ6ICR7ZXJyLm1lc3NhZ2V9YDtcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG5cbiAgICAgIC8vIEF1dG8tY2xlYXIgZXJyb3IgbWVzc2FnZSBhZnRlciA4IHNlY29uZHNcbiAgICAgIHNldFRpbWVvdXQoKCkgPT4gc2V0RXJyb3IobnVsbCksIDgwMDApO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1VwbG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRVcGxvYWRQcm9ncmVzcygwKTtcbiAgICAgIFxuICAgICAgLy8gQ2xlYXIgZmlsZSBpbnB1dFxuICAgICAgaWYgKGZpbGVJbnB1dFJlZi5jdXJyZW50KSB7XG4gICAgICAgIGZpbGVJbnB1dFJlZi5jdXJyZW50LnZhbHVlID0gJyc7XG4gICAgICB9XG5cbiAgICAgIC8vIEF1dG8tY2xlYXIgc3VjY2VzcyBtZXNzYWdlIGFmdGVyIDUgc2Vjb25kc1xuICAgICAgaWYgKHN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiBzZXRTdWNjZXNzKG51bGwpLCA1MDAwKTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGRyYWcgYW5kIGRyb3BcbiAgY29uc3QgaGFuZGxlRHJhZyA9IHVzZUNhbGxiYWNrKChlOiBSZWFjdC5EcmFnRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICBpZiAoZS50eXBlID09PSAnZHJhZ2VudGVyJyB8fCBlLnR5cGUgPT09ICdkcmFnb3ZlcicpIHtcbiAgICAgIHNldERyYWdBY3RpdmUodHJ1ZSk7XG4gICAgfSBlbHNlIGlmIChlLnR5cGUgPT09ICdkcmFnbGVhdmUnKSB7XG4gICAgICBzZXREcmFnQWN0aXZlKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gdXNlQ2FsbGJhY2soKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIHNldERyYWdBY3RpdmUoZmFsc2UpO1xuICAgIFxuICAgIGlmIChlLmRhdGFUcmFuc2Zlci5maWxlcyAmJiBlLmRhdGFUcmFuc2Zlci5maWxlc1swXSkge1xuICAgICAgaGFuZGxlRmlsZVVwbG9hZChlLmRhdGFUcmFuc2Zlci5maWxlcyk7XG4gICAgfVxuICB9LCBbY29uZmlnSWRdKTtcblxuICAvLyBIYW5kbGUgZmlsZSBpbnB1dCBjaGFuZ2VcbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBpZiAoZS50YXJnZXQuZmlsZXMgJiYgZS50YXJnZXQuZmlsZXNbMF0pIHtcbiAgICAgIGhhbmRsZUZpbGVVcGxvYWQoZS50YXJnZXQuZmlsZXMpO1xuICAgIH1cbiAgfTtcblxuICAvLyBEZWxldGUgZG9jdW1lbnRcbiAgY29uc3QgaGFuZGxlRGVsZXRlRG9jdW1lbnQgPSAoZG9jdW1lbnRJZDogc3RyaW5nLCBkb2N1bWVudE5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbmZpcm1hdGlvbi5zaG93Q29uZmlybWF0aW9uKFxuICAgICAge1xuICAgICAgICB0aXRsZTogJ0RlbGV0ZSBEb2N1bWVudCcsXG4gICAgICAgIG1lc3NhZ2U6IGBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIFwiJHtkb2N1bWVudE5hbWV9XCI/IFRoaXMgd2lsbCBwZXJtYW5lbnRseSByZW1vdmUgdGhlIGRvY3VtZW50IGFuZCBhbGwgaXRzIHByb2Nlc3NlZCBjaHVua3MgZnJvbSB5b3VyIGtub3dsZWRnZSBiYXNlLiBUaGlzIGFjdGlvbiBjYW5ub3QgYmUgdW5kb25lLmAsXG4gICAgICAgIGNvbmZpcm1UZXh0OiAnRGVsZXRlIERvY3VtZW50JyxcbiAgICAgICAgY2FuY2VsVGV4dDogJ0NhbmNlbCcsXG4gICAgICAgIHR5cGU6ICdkYW5nZXInXG4gICAgICB9LFxuICAgICAgYXN5bmMgKCkgPT4ge1xuICAgICAgICAvLyBPcHRpbWlzdGljYWxseSByZW1vdmUgdGhlIGRvY3VtZW50IGZyb20gdGhlIGxpc3RcbiAgICAgICAgY29uc3Qgb3JpZ2luYWxEb2N1bWVudHMgPSBkb2N1bWVudHM7XG4gICAgICAgIHNldERvY3VtZW50cyhwcmV2ID0+IHByZXYuZmlsdGVyKGRvYyA9PiBkb2MuaWQgIT09IGRvY3VtZW50SWQpKTtcblxuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvZG9jdW1lbnRzLyR7ZG9jdW1lbnRJZH1gLCB7XG4gICAgICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgICAgLy8gUmVzdG9yZSB0aGUgb3JpZ2luYWwgbGlzdCBvbiBlcnJvclxuICAgICAgICAgICAgc2V0RG9jdW1lbnRzKG9yaWdpbmFsRG9jdW1lbnRzKTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGRlbGV0ZSBkb2N1bWVudCcpO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIHNldFN1Y2Nlc3MoJ0RvY3VtZW50IGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG5cbiAgICAgICAgICAvLyBBdXRvLWNsZWFyIHN1Y2Nlc3MgbWVzc2FnZSBhZnRlciAzIHNlY29uZHNcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHNldFN1Y2Nlc3MobnVsbCksIDMwMDApO1xuICAgICAgICB9IGNhdGNoIChlcnI6IGFueSkge1xuICAgICAgICAgIC8vIFJlc3RvcmUgdGhlIG9yaWdpbmFsIGxpc3Qgb24gZXJyb3JcbiAgICAgICAgICBzZXREb2N1bWVudHMob3JpZ2luYWxEb2N1bWVudHMpO1xuICAgICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGBEZWxldGUgZmFpbGVkOiAke2Vyci5tZXNzYWdlfWA7XG4gICAgICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcblxuICAgICAgICAgIC8vIEF1dG8tY2xlYXIgZXJyb3IgbWVzc2FnZSBhZnRlciA4IHNlY29uZHNcbiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHNldEVycm9yKG51bGwpLCA4MDAwKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICk7XG4gIH07XG5cbiAgLy8gRm9ybWF0IGZpbGUgc2l6ZVxuICBjb25zdCBmb3JtYXRGaWxlU2l6ZSA9IChieXRlczogbnVtYmVyKSA9PiB7XG4gICAgaWYgKGJ5dGVzID09PSAwKSByZXR1cm4gJzAgQnl0ZXMnO1xuICAgIGNvbnN0IGsgPSAxMDI0O1xuICAgIGNvbnN0IHNpemVzID0gWydCeXRlcycsICdLQicsICdNQicsICdHQiddO1xuICAgIGNvbnN0IGkgPSBNYXRoLmZsb29yKE1hdGgubG9nKGJ5dGVzKSAvIE1hdGgubG9nKGspKTtcbiAgICByZXR1cm4gcGFyc2VGbG9hdCgoYnl0ZXMgLyBNYXRoLnBvdyhrLCBpKSkudG9GaXhlZCgyKSkgKyAnICcgKyBzaXplc1tpXTtcbiAgfTtcblxuICAvLyBHZXQgZmlsZSBpY29uXG4gIGNvbnN0IGdldEZpbGVJY29uID0gKGZpbGVUeXBlOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoZmlsZVR5cGUuaW5jbHVkZXMoJ3BkZicpKSByZXR1cm4gPEZpbGVUZXh0IGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1yZWQtNTAwXCIgLz47XG4gICAgaWYgKGZpbGVUeXBlLmluY2x1ZGVzKCd3b3JkJykpIHJldHVybiA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWJsdWUtNTAwXCIgLz47XG4gICAgcmV0dXJuIDxGaWxlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmF5LTUwMFwiIC8+O1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBFcnJvci9TdWNjZXNzIE1lc3NhZ2VzICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQteGwgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcmVkLTYwMFwiIC8+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXJlZC04MDAgdGV4dC1zbSBmb250LW1lZGl1bVwiPntlcnJvcn08L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAge3N1Y2Nlc3MgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwIHJvdW5kZWQteGwgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tODAwIHRleHQtc20gZm9udC1tZWRpdW1cIj57c3VjY2Vzc308L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFVwbG9hZCBBcmVhICovfVxuICAgICAgPGRpdlxuICAgICAgICBjbGFzc05hbWU9e2ByZWxhdGl2ZSBib3JkZXItMiBib3JkZXItZGFzaGVkIHJvdW5kZWQtMnhsIHAtOCB0ZXh0LWNlbnRlciB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdHJhbnNmb3JtICR7XG4gICAgICAgICAgZHJhZ0FjdGl2ZVxuICAgICAgICAgICAgPyAnYm9yZGVyLW9yYW5nZS00MDAgYmctb3JhbmdlLTUwIHNjYWxlLTEwNSBzaGFkb3ctbGcnXG4gICAgICAgICAgICA6ICdib3JkZXItZ3JheS0zMDAgaG92ZXI6Ym9yZGVyLW9yYW5nZS00MDAgaG92ZXI6Ymctb3JhbmdlLTUwIGhvdmVyOnNjYWxlLTEwMiBob3ZlcjpzaGFkb3ctbWQnXG4gICAgICAgIH0gJHshY29uZmlnSWQgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJ2N1cnNvci1wb2ludGVyJ31gfVxuICAgICAgICBvbkRyYWdFbnRlcj17aGFuZGxlRHJhZ31cbiAgICAgICAgb25EcmFnTGVhdmU9e2hhbmRsZURyYWd9XG4gICAgICAgIG9uRHJhZ092ZXI9e2hhbmRsZURyYWd9XG4gICAgICAgIG9uRHJvcD17aGFuZGxlRHJvcH1cbiAgICAgICAgb25DbGljaz17KCkgPT4gY29uZmlnSWQgJiYgZmlsZUlucHV0UmVmLmN1cnJlbnQ/LmNsaWNrKCl9XG4gICAgICA+XG4gICAgICAgIDxpbnB1dFxuICAgICAgICAgIHJlZj17ZmlsZUlucHV0UmVmfVxuICAgICAgICAgIHR5cGU9XCJmaWxlXCJcbiAgICAgICAgICBjbGFzc05hbWU9XCJoaWRkZW5cIlxuICAgICAgICAgIGFjY2VwdD1cIi5wZGYsLnR4dCwubWRcIlxuICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cbiAgICAgICAgICBkaXNhYmxlZD17IWNvbmZpZ0lkIHx8IGlzVXBsb2FkaW5nfVxuICAgICAgICAvPlxuXG4gICAgICAgIHtpc1VwbG9hZGluZyA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtb3JhbmdlLTUwMCBteC1hdXRvIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5Qcm9jZXNzaW5nIERvY3VtZW50Li4uPC9wPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTIwMCByb3VuZGVkLWZ1bGwgaC0yXCI+XG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctb3JhbmdlLTUwMCBoLTIgcm91bmRlZC1mdWxsIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7dXBsb2FkUHJvZ3Jlc3N9JWAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+e3VwbG9hZFByb2dyZXNzfSUgY29tcGxldGU8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPFVwbG9hZCBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTQwMCBteC1hdXRvXCIgLz5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgIHtjb25maWdJZCA/ICdVcGxvYWQgS25vd2xlZGdlIERvY3VtZW50cycgOiAnU2VsZWN0IGEgY29uZmlndXJhdGlvbiBmaXJzdCd9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICBEcmFnIGFuZCBkcm9wIGZpbGVzIGhlcmUsIG9yIGNsaWNrIHRvIGJyb3dzZVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMCBtdC0yXCI+XG4gICAgICAgICAgICAgICAgU3VwcG9ydHMgUERGLCBUWFQsIE1EIGZpbGVzIHVwIHRvIDEwTUJcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIERvY3VtZW50cyBMaXN0ICovfVxuICAgICAge2RvY3VtZW50cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+VXBsb2FkZWQgRG9jdW1lbnRzPC9oMz5cbiAgICAgICAgICAgIHtpc1JlZnJlc2hpbmcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiB0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJ3LTQgaC00IGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+UmVmcmVzaGluZy4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNFwiPlxuICAgICAgICAgICAge2RvY3VtZW50cy5tYXAoKGRvYykgPT4gKFxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAga2V5PXtkb2MuaWR9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtNCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQteGwgaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tc2hhZG93XCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgICB7Z2V0RmlsZUljb24oZG9jLmZpbGVfdHlwZSl9XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e2RvYy5maWxlbmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRGaWxlU2l6ZShkb2MuZmlsZV9zaXplKX0g4oCiIHtkb2MuY2h1bmtzX2NvdW50fSBjaHVua3NcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIHtkb2Muc3RhdHVzID09PSAnY29tcGxldGVkJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICB7ZG9jLnN0YXR1cyA9PT0gJ3Byb2Nlc3NpbmcnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtb3JhbmdlLTUwMCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICB7ZG9jLnN0YXR1cyA9PT0gJ2ZhaWxlZCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtcmVkLTUwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQtc20gZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICBkb2Muc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICd0ZXh0LWdyZWVuLTYwMCcgOlxuICAgICAgICAgICAgICAgICAgICAgIGRvYy5zdGF0dXMgPT09ICdwcm9jZXNzaW5nJyA/ICd0ZXh0LW9yYW5nZS02MDAnIDpcbiAgICAgICAgICAgICAgICAgICAgICAndGV4dC1yZWQtNjAwJ1xuICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge2RvYy5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gJ1JlYWR5JyA6XG4gICAgICAgICAgICAgICAgICAgICAgIGRvYy5zdGF0dXMgPT09ICdwcm9jZXNzaW5nJyA/ICdQcm9jZXNzaW5nJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICdGYWlsZWQnfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVEb2N1bWVudChkb2MuaWQsIGRvYy5maWxlbmFtZSl9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtcmVkLTUwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRGVsZXRlIGRvY3VtZW50XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogQ29uZmlybWF0aW9uIE1vZGFsICovfVxuICAgICAgPENvbmZpcm1hdGlvbk1vZGFsXG4gICAgICAgIGlzT3Blbj17Y29uZmlybWF0aW9uLmlzT3Blbn1cbiAgICAgICAgb25DbG9zZT17Y29uZmlybWF0aW9uLmhpZGVDb25maXJtYXRpb259XG4gICAgICAgIG9uQ29uZmlybT17Y29uZmlybWF0aW9uLm9uQ29uZmlybX1cbiAgICAgICAgdGl0bGU9e2NvbmZpcm1hdGlvbi50aXRsZX1cbiAgICAgICAgbWVzc2FnZT17Y29uZmlybWF0aW9uLm1lc3NhZ2V9XG4gICAgICAgIGNvbmZpcm1UZXh0PXtjb25maXJtYXRpb24uY29uZmlybVRleHR9XG4gICAgICAgIGNhbmNlbFRleHQ9e2NvbmZpcm1hdGlvbi5jYW5jZWxUZXh0fVxuICAgICAgICB0eXBlPXtjb25maXJtYXRpb24udHlwZX1cbiAgICAgICAgaXNMb2FkaW5nPXtjb25maXJtYXRpb24uaXNMb2FkaW5nfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJVcGxvYWQiLCJGaWxlIiwiWCIsIkNoZWNrQ2lyY2xlIiwiQWxlcnRDaXJjbGUiLCJGaWxlVGV4dCIsIkxvYWRlcjIiLCJ1c2VDb25maXJtYXRpb24iLCJDb25maXJtYXRpb25Nb2RhbCIsIkRvY3VtZW50VXBsb2FkIiwiY29uZmlnSWQiLCJvbkRvY3VtZW50VXBsb2FkZWQiLCJvbkRvY3VtZW50RGVsZXRlZCIsImRvY3VtZW50cyIsInNldERvY3VtZW50cyIsImlzVXBsb2FkaW5nIiwic2V0SXNVcGxvYWRpbmciLCJpc1JlZnJlc2hpbmciLCJzZXRJc1JlZnJlc2hpbmciLCJ1cGxvYWRQcm9ncmVzcyIsInNldFVwbG9hZFByb2dyZXNzIiwiZHJhZ0FjdGl2ZSIsInNldERyYWdBY3RpdmUiLCJlcnJvciIsInNldEVycm9yIiwic3VjY2VzcyIsInNldFN1Y2Nlc3MiLCJmaWxlSW5wdXRSZWYiLCJjb25maXJtYXRpb24iLCJsb2FkRG9jdW1lbnRzIiwicmV0cnlDb3VudCIsImV4cGVjdGVkRG9jdW1lbnRJZCIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsImRhdGEiLCJqc29uIiwibmV3RG9jdW1lbnRzIiwiZm91bmREb2N1bWVudCIsImZpbmQiLCJkb2MiLCJpZCIsImNvbnNvbGUiLCJsb2ciLCJzZXRUaW1lb3V0Iiwic29ydCIsImEiLCJiIiwiRGF0ZSIsImNyZWF0ZWRfYXQiLCJnZXRUaW1lIiwiZXJyIiwidXNlRWZmZWN0IiwiaGFuZGxlRmlsZVVwbG9hZCIsImZpbGVzIiwiZmlsZSIsImFsbG93ZWRUeXBlcyIsImluY2x1ZGVzIiwidHlwZSIsInNpemUiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwicHJvZ3Jlc3NJbnRlcnZhbCIsInNldEludGVydmFsIiwicHJldiIsIk1hdGgiLCJtaW4iLCJtZXRob2QiLCJib2R5IiwiY2xlYXJJbnRlcnZhbCIsImVycm9yRGF0YSIsIkVycm9yIiwicmVzdWx0IiwibmFtZSIsImRvY3VtZW50IiwiY2h1bmtzX3RvdGFsIiwib3B0aW1pc3RpY0RvY3VtZW50IiwiZmlsZW5hbWUiLCJmaWxlX3R5cGUiLCJmaWxlX3NpemUiLCJzdGF0dXMiLCJjaHVua3NfY291bnQiLCJjaHVua3NfcHJvY2Vzc2VkIiwidG9JU09TdHJpbmciLCJleGlzdHMiLCJtYXAiLCJlcnJvck1lc3NhZ2UiLCJtZXNzYWdlIiwiY3VycmVudCIsInZhbHVlIiwiaGFuZGxlRHJhZyIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInN0b3BQcm9wYWdhdGlvbiIsImhhbmRsZURyb3AiLCJkYXRhVHJhbnNmZXIiLCJoYW5kbGVJbnB1dENoYW5nZSIsInRhcmdldCIsImhhbmRsZURlbGV0ZURvY3VtZW50IiwiZG9jdW1lbnRJZCIsImRvY3VtZW50TmFtZSIsInNob3dDb25maXJtYXRpb24iLCJ0aXRsZSIsImNvbmZpcm1UZXh0IiwiY2FuY2VsVGV4dCIsIm9yaWdpbmFsRG9jdW1lbnRzIiwiZmlsdGVyIiwiZm9ybWF0RmlsZVNpemUiLCJieXRlcyIsImsiLCJzaXplcyIsImkiLCJmbG9vciIsInBhcnNlRmxvYXQiLCJwb3ciLCJ0b0ZpeGVkIiwiZ2V0RmlsZUljb24iLCJmaWxlVHlwZSIsImNsYXNzTmFtZSIsImRpdiIsInAiLCJvbkRyYWdFbnRlciIsIm9uRHJhZ0xlYXZlIiwib25EcmFnT3ZlciIsIm9uRHJvcCIsIm9uQ2xpY2siLCJjbGljayIsImlucHV0IiwicmVmIiwiYWNjZXB0Iiwib25DaGFuZ2UiLCJkaXNhYmxlZCIsInN0eWxlIiwid2lkdGgiLCJsZW5ndGgiLCJoMyIsInNwYW4iLCJidXR0b24iLCJpc09wZW4iLCJvbkNsb3NlIiwiaGlkZUNvbmZpcm1hdGlvbiIsIm9uQ29uZmlybSIsImlzTG9hZGluZyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});