/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/training/jobs/route";
exports.ids = ["app/api/training/jobs/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftraining%2Fjobs%2Froute&page=%2Fapi%2Ftraining%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftraining%2Fjobs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftraining%2Fjobs%2Froute&page=%2Fapi%2Ftraining%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftraining%2Fjobs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_training_jobs_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/training/jobs/route.ts */ \"(rsc)/./src/app/api/training/jobs/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/training/jobs/route\",\n        pathname: \"/api/training/jobs\",\n        filename: \"route\",\n        bundlePath: \"app/api/training/jobs/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\training\\\\jobs\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_training_jobs_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftraining%2Fjobs%2Froute&page=%2Fapi%2Ftraining%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftraining%2Fjobs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/training/jobs/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/training/jobs/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stripe-client */ \"(rsc)/./src/lib/stripe-client.ts\");\n\n\n\n// GET /api/training/jobs?custom_api_config_id=<ID>&active_only=true\n// Retrieves training jobs for a specific custom_api_config_id\n// If active_only=true, returns only the most recent completed training data for chat enhancement\nasync function GET(request) {\n    const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n    // Check authentication\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication failed in /api/training/jobs:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view training jobs.'\n        }, {\n            status: 401\n        });\n    }\n    const { searchParams } = new URL(request.url);\n    const customApiConfigId = searchParams.get('custom_api_config_id');\n    const activeOnly = searchParams.get('active_only') === 'true';\n    if (!customApiConfigId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'custom_api_config_id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        let query = supabase.from('training_jobs').select('*').eq('custom_api_config_id', customApiConfigId);\n        if (activeOnly) {\n            // Get only the most recent completed training job for chat enhancement\n            query = query.eq('status', 'completed').order('created_at', {\n                ascending: false\n            }).limit(1);\n        } else {\n            // Get all training jobs for management view\n            query = query.order('created_at', {\n                ascending: false\n            });\n        }\n        const { data: jobs, error } = await query;\n        if (error) {\n            console.error('Supabase error fetching training jobs:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch training jobs',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        if (activeOnly && jobs && jobs.length > 0) {\n            // Return the training data in a format ready for chat enhancement\n            const latestJob = jobs[0];\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                has_training: true,\n                training_data: latestJob.training_data,\n                job_id: latestJob.id,\n                created_at: latestJob.created_at\n            }, {\n                status: 200\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(activeOnly ? {\n            has_training: false\n        } : jobs || [], {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in GET /api/training/jobs:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/training/jobs\n// Creates a new training job - WITH SAFEGUARD AGAINST DUPLICATE CREATION\nasync function POST(request) {\n    const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n    // Check authentication\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication failed in /api/training/jobs:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create training jobs.'\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const jobData = await request.json();\n        const { custom_api_config_id, name, description, training_data, parameters } = jobData;\n        if (!custom_api_config_id || !name) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields: custom_api_config_id, name'\n            }, {\n                status: 400\n            });\n        }\n        // Check user's subscription tier for prompt engineering access\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Check if user has access to prompt engineering feature\n        if (!(0,_lib_stripe_client__WEBPACK_IMPORTED_MODULE_2__.hasFeatureAccess)(userTier, 'prompt_engineering')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Prompt engineering is not available on the ${userTier} plan. Please upgrade to create training jobs.`\n            }, {\n                status: 403\n            });\n        }\n        // SAFEGUARD: Check if a training job already exists for this config\n        // This prevents accidental creation of duplicate jobs that would trigger CASCADE DELETE\n        console.log(`[Training Job Creation] Checking for existing jobs for config: ${custom_api_config_id}`);\n        const { data: existingJobs, error: checkError } = await supabase.from('training_jobs').select('id, name, status, created_at').eq('custom_api_config_id', custom_api_config_id).order('created_at', {\n            ascending: false\n        }).limit(1);\n        if (checkError) {\n            console.error('[Training Job Creation] Error checking for existing jobs:', checkError);\n        // Continue with creation if check fails - don't block the operation\n        } else if (existingJobs && existingJobs.length > 0) {\n            const existingJob = existingJobs[0];\n            console.warn(`[Training Job Creation] WARNING: Training job already exists for config ${custom_api_config_id}:`, existingJob);\n            console.warn('[Training Job Creation] Consider using PUT to update instead of creating new job');\n            // Return the existing job instead of creating a new one\n            // This prevents CASCADE DELETE of training files\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                ...existingJob,\n                warning: 'Training job already exists for this configuration. Returned existing job to prevent data loss.',\n                recommendation: 'Use PUT method to update existing training job instead.'\n            }, {\n                status: 200\n            });\n        }\n        console.log(`[Training Job Creation] No existing job found, creating new training job for config: ${custom_api_config_id}`);\n        const { data, error } = await supabase.from('training_jobs').insert({\n            custom_api_config_id,\n            name,\n            description,\n            training_data,\n            parameters,\n            status: 'completed',\n            progress_percentage: 100,\n            started_at: new Date().toISOString(),\n            completed_at: new Date().toISOString(),\n            user_id: user.id\n        }).select().single();\n        if (error) {\n            console.error('Supabase error creating training job:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create training job',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(`[Training Job Creation] Successfully created new training job:`, data.id);\n        // CRITICAL: Invalidate training cache for immediate effect\n        try {\n            const { trainingDataCache } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_cache_trainingCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/cache/trainingCache */ \"(rsc)/./src/lib/cache/trainingCache.ts\"));\n            const invalidated = trainingDataCache.invalidate(custom_api_config_id);\n            console.log(`[Training Job Creation] Cache invalidated for config: ${custom_api_config_id} (${invalidated ? 'success' : 'not cached'})`);\n        } catch (error) {\n            console.error(`[Training Job Creation] CRITICAL: Cache invalidation failed for config: ${custom_api_config_id}`, error);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/training/jobs:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT /api/training/jobs?id=<ID>\n// Updates a training job - ENHANCED WITH VALIDATION AND LOGGING\nasync function PUT(request) {\n    const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n    const { searchParams } = new URL(request.url);\n    const jobId = searchParams.get('id');\n    if (!jobId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        const updateData = await request.json();\n        console.log(`[Training Job Update] Updating training job ${jobId} with data:`, updateData);\n        // Verify the job exists before updating\n        const { data: existingJob, error: checkError } = await supabase.from('training_jobs').select('id, name, status, custom_api_config_id, created_at').eq('id', jobId).single();\n        if (checkError) {\n            console.error(`[Training Job Update] Error checking existing job ${jobId}:`, checkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to verify training job exists',\n                details: checkError.message\n            }, {\n                status: 500\n            });\n        }\n        if (!existingJob) {\n            console.error(`[Training Job Update] Training job ${jobId} not found`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Training job not found'\n            }, {\n                status: 404\n            });\n        }\n        console.log(`[Training Job Update] Found existing job:`, existingJob);\n        // Perform the update\n        const { data, error } = await supabase.from('training_jobs').update({\n            ...updateData,\n            updated_at: new Date().toISOString() // Ensure updated_at is always set\n        }).eq('id', jobId).select().single();\n        if (error) {\n            console.error(`[Training Job Update] Supabase error updating training job ${jobId}:`, error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to update training job',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        console.log(`[Training Job Update] Successfully updated training job ${jobId}:`, data);\n        // CRITICAL: Invalidate training cache for immediate effect\n        try {\n            const { trainingDataCache } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_cache_trainingCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/cache/trainingCache */ \"(rsc)/./src/lib/cache/trainingCache.ts\"));\n            const invalidated = trainingDataCache.invalidate(existingJob.custom_api_config_id);\n            console.log(`[Training Job Update] Cache invalidated for config: ${existingJob.custom_api_config_id} (${invalidated ? 'success' : 'not cached'})`);\n        } catch (error) {\n            console.error(`[Training Job Update] CRITICAL: Cache invalidation failed for config: ${existingJob.custom_api_config_id}`, error);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 200\n        });\n    } catch (e) {\n        console.error(`[Training Job Update] Error in PUT /api/training/jobs for job ${jobId}:`, e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/training/jobs?id=<ID>\n// Deletes a training job and all associated files\nasync function DELETE(request) {\n    const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n    const { searchParams } = new URL(request.url);\n    const jobId = searchParams.get('id');\n    if (!jobId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // First get the job details for cache invalidation\n        const { data: jobToDelete, error: fetchError } = await supabase.from('training_jobs').select('custom_api_config_id').eq('id', jobId).single();\n        if (fetchError) {\n            console.error('Error fetching training job for deletion:', fetchError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch training job',\n                details: fetchError.message\n            }, {\n                status: 500\n            });\n        }\n        // Delete training job (files will be deleted automatically due to CASCADE)\n        const { error } = await supabase.from('training_jobs').delete().eq('id', jobId);\n        if (error) {\n            console.error('Supabase error deleting training job:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to delete training job',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // CRITICAL: Invalidate training cache after deletion\n        if (jobToDelete) {\n            try {\n                const { trainingDataCache } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_cache_trainingCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/cache/trainingCache */ \"(rsc)/./src/lib/cache/trainingCache.ts\"));\n                const invalidated = trainingDataCache.invalidate(jobToDelete.custom_api_config_id);\n                console.log(`[Training Job Deletion] Cache invalidated for config: ${jobToDelete.custom_api_config_id} (${invalidated ? 'success' : 'not cached'})`);\n            } catch (error) {\n                console.error(`[Training Job Deletion] CRITICAL: Cache invalidation failed for config: ${jobToDelete.custom_api_config_id}`, error);\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Training job deleted successfully'\n        }, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in DELETE /api/training/jobs:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/training/jobs/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-client.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-client.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TIER_CONFIGS: () => (/* binding */ TIER_CONFIGS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   getPriceIdForTier: () => (/* binding */ getPriceIdForTier),\n/* harmony export */   getTierConfig: () => (/* binding */ getTierConfig),\n/* harmony export */   getTierFromPriceId: () => (/* binding */ getTierFromPriceId),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess)\n/* harmony export */ });\n/* harmony import */ var _stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./stripe-config */ \"(rsc)/./src/lib/stripe-config.ts\");\n// Client-safe Stripe utilities (no server-side Stripe instance)\n\nconst TIER_CONFIGS = {\n    free: {\n        name: 'Free',\n        price: '$0',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.FREE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.FREE,\n        features: [\n            'Unlimited API requests',\n            '1 Custom Configuration',\n            '3 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback routing only',\n            'Basic analytics only',\n            'No custom roles, basic router only',\n            'Limited logs',\n            'Community support'\n        ],\n        limits: {\n            configurations: 1,\n            apiKeysPerConfig: 3,\n            apiRequests: 999999,\n            canUseAdvancedRouting: false,\n            canUseCustomRoles: false,\n            maxCustomRoles: 0,\n            canUsePromptEngineering: false,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    starter: {\n        name: 'Starter',\n        price: '$19',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.STARTER,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.STARTER,\n        features: [\n            'Unlimited API requests',\n            '4 Custom Configurations',\n            '5 API Keys per config',\n            'All 300+ AI models',\n            'Strict fallback + Complex routing (1 config limit)',\n            'Up to 3 custom roles',\n            'Intelligent role routing (1 config)',\n            'Prompt engineering (no file upload)',\n            'Enhanced logs and analytics',\n            'Community support'\n        ],\n        limits: {\n            configurations: 4,\n            apiKeysPerConfig: 5,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 3,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: false,\n            knowledgeBaseDocuments: 0,\n            canUseSemanticCaching: false\n        }\n    },\n    professional: {\n        name: 'Professional',\n        price: '$49',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.PROFESSIONAL,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.PROFESSIONAL,\n        features: [\n            'Unlimited API requests',\n            '20 Custom Configurations',\n            '15 API Keys per config',\n            'All 300+ AI models',\n            'All advanced routing strategies',\n            'Unlimited custom roles',\n            'Prompt engineering + Knowledge base (5 documents)',\n            'Semantic caching',\n            'Advanced analytics and logging',\n            'Priority email support'\n        ],\n        limits: {\n            configurations: 20,\n            apiKeysPerConfig: 15,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 5,\n            canUseSemanticCaching: true\n        }\n    },\n    enterprise: {\n        name: 'Enterprise',\n        price: '$149',\n        priceId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRICE_IDS.ENTERPRISE,\n        productId: _stripe_config__WEBPACK_IMPORTED_MODULE_0__.STRIPE_PRODUCT_IDS.ENTERPRISE,\n        features: [\n            'Unlimited API requests',\n            'Unlimited configurations',\n            'Unlimited API keys',\n            'All 300+ models + priority access',\n            'All routing strategies',\n            'Unlimited custom roles',\n            'All features + priority support',\n            'Unlimited knowledge base documents',\n            'Advanced semantic caching',\n            'Custom integrations',\n            'Dedicated support + phone',\n            'SLA guarantee'\n        ],\n        limits: {\n            configurations: 999999,\n            apiKeysPerConfig: 999999,\n            apiRequests: 999999,\n            canUseAdvancedRouting: true,\n            canUseCustomRoles: true,\n            maxCustomRoles: 999999,\n            canUsePromptEngineering: true,\n            canUseKnowledgeBase: true,\n            knowledgeBaseDocuments: 999999,\n            canUseSemanticCaching: true\n        }\n    }\n};\nfunction getTierConfig(tier) {\n    return TIER_CONFIGS[tier];\n}\nfunction getPriceIdForTier(tier) {\n    return TIER_CONFIGS[tier].priceId;\n}\nfunction getTierFromPriceId(priceId) {\n    for (const [tier, config] of Object.entries(TIER_CONFIGS)){\n        if (config.priceId === priceId) {\n            return tier;\n        }\n    }\n    return 'free'; // Default fallback to free tier\n}\nfunction formatPrice(tier) {\n    return TIER_CONFIGS[tier].price;\n}\nfunction canPerformAction(tier, action, currentCount) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(action){\n        case 'create_config':\n            return currentCount < limits.configurations;\n        case 'create_api_key':\n            return currentCount < limits.apiKeysPerConfig;\n        default:\n            return true;\n    }\n}\nfunction hasFeatureAccess(tier, feature) {\n    const limits = TIER_CONFIGS[tier].limits;\n    switch(feature){\n        case 'custom_roles':\n            return limits.canUseCustomRoles;\n        case 'knowledge_base':\n            return limits.canUseKnowledgeBase;\n        case 'advanced_routing':\n            return limits.canUseAdvancedRouting;\n        case 'prompt_engineering':\n            return limits.canUsePromptEngineering;\n        case 'semantic_caching':\n            return limits.canUseSemanticCaching;\n        case 'configurations':\n            return limits.configurations > 0; // All tiers can create at least some configurations\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS1jbGllbnQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSxnRUFBZ0U7QUFDTztBQXdCaEUsTUFBTUUsZUFBcUQ7SUFDaEVDLE1BQU07UUFDSkMsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFNBQVNOLDREQUFnQkEsQ0FBQ08sSUFBSTtRQUM5QkMsV0FBV1AsOERBQWtCQSxDQUFDTSxJQUFJO1FBQ2xDRSxVQUFVO1lBQ1I7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtZQUNOQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsYUFBYTtZQUNiQyx1QkFBdUI7WUFDdkJDLG1CQUFtQjtZQUNuQkMsZ0JBQWdCO1lBQ2hCQyx5QkFBeUI7WUFDekJDLHFCQUFxQjtZQUNyQkMsd0JBQXdCO1lBQ3hCQyx1QkFBdUI7UUFDekI7SUFDRjtJQUNBQyxTQUFTO1FBQ1BqQixNQUFNO1FBQ05DLE9BQU87UUFDUEMsU0FBU04sNERBQWdCQSxDQUFDc0IsT0FBTztRQUNqQ2QsV0FBV1AsOERBQWtCQSxDQUFDcUIsT0FBTztRQUNyQ2IsVUFBVTtZQUNSO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDREMsUUFBUTtZQUNOQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsYUFBYTtZQUNiQyx1QkFBdUI7WUFDdkJDLG1CQUFtQjtZQUNuQkMsZ0JBQWdCO1lBQ2hCQyx5QkFBeUI7WUFDekJDLHFCQUFxQjtZQUNyQkMsd0JBQXdCO1lBQ3hCQyx1QkFBdUI7UUFDekI7SUFDRjtJQUNBRyxjQUFjO1FBQ1puQixNQUFNO1FBQ05DLE9BQU87UUFDUEMsU0FBU04sNERBQWdCQSxDQUFDd0IsWUFBWTtRQUN0Q2hCLFdBQVdQLDhEQUFrQkEsQ0FBQ3VCLFlBQVk7UUFDMUNmLFVBQVU7WUFDUjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7WUFDTkMsZ0JBQWdCO1lBQ2hCQyxrQkFBa0I7WUFDbEJDLGFBQWE7WUFDYkMsdUJBQXVCO1lBQ3ZCQyxtQkFBbUI7WUFDbkJDLGdCQUFnQjtZQUNoQkMseUJBQXlCO1lBQ3pCQyxxQkFBcUI7WUFDckJDLHdCQUF3QjtZQUN4QkMsdUJBQXVCO1FBQ3pCO0lBQ0Y7SUFDQUssWUFBWTtRQUNWckIsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFNBQVNOLDREQUFnQkEsQ0FBQzBCLFVBQVU7UUFDcENsQixXQUFXUCw4REFBa0JBLENBQUN5QixVQUFVO1FBQ3hDakIsVUFBVTtZQUNSO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBQ0RDLFFBQVE7WUFDTkMsZ0JBQWdCO1lBQ2hCQyxrQkFBa0I7WUFDbEJDLGFBQWE7WUFDYkMsdUJBQXVCO1lBQ3ZCQyxtQkFBbUI7WUFDbkJDLGdCQUFnQjtZQUNoQkMseUJBQXlCO1lBQ3pCQyxxQkFBcUI7WUFDckJDLHdCQUF3QjtZQUN4QkMsdUJBQXVCO1FBQ3pCO0lBQ0Y7QUFDRixFQUFFO0FBRUssU0FBU08sY0FBY0MsSUFBc0I7SUFDbEQsT0FBTzFCLFlBQVksQ0FBQzBCLEtBQUs7QUFDM0I7QUFFTyxTQUFTQyxrQkFBa0JELElBQXNCO0lBQ3RELE9BQU8xQixZQUFZLENBQUMwQixLQUFLLENBQUN0QixPQUFPO0FBQ25DO0FBRU8sU0FBU3dCLG1CQUFtQnhCLE9BQWU7SUFDaEQsS0FBSyxNQUFNLENBQUNzQixNQUFNRyxPQUFPLElBQUlDLE9BQU9DLE9BQU8sQ0FBQy9CLGNBQWU7UUFDekQsSUFBSTZCLE9BQU96QixPQUFPLEtBQUtBLFNBQVM7WUFDOUIsT0FBT3NCO1FBQ1Q7SUFDRjtJQUNBLE9BQU8sUUFBUSxnQ0FBZ0M7QUFDakQ7QUFFTyxTQUFTTSxZQUFZTixJQUFzQjtJQUNoRCxPQUFPMUIsWUFBWSxDQUFDMEIsS0FBSyxDQUFDdkIsS0FBSztBQUNqQztBQUVPLFNBQVM4QixpQkFDZFAsSUFBc0IsRUFDdEJRLE1BQTBDLEVBQzFDQyxZQUFvQjtJQUVwQixNQUFNM0IsU0FBU1IsWUFBWSxDQUFDMEIsS0FBSyxDQUFDbEIsTUFBTTtJQUV4QyxPQUFRMEI7UUFDTixLQUFLO1lBQ0gsT0FBT0MsZUFBZTNCLE9BQU9DLGNBQWM7UUFDN0MsS0FBSztZQUNILE9BQU8wQixlQUFlM0IsT0FBT0UsZ0JBQWdCO1FBQy9DO1lBQ0UsT0FBTztJQUNYO0FBQ0Y7QUFFTyxTQUFTMEIsaUJBQ2RWLElBQXNCLEVBQ3RCVyxPQUE4SDtJQUU5SCxNQUFNN0IsU0FBU1IsWUFBWSxDQUFDMEIsS0FBSyxDQUFDbEIsTUFBTTtJQUV4QyxPQUFRNkI7UUFDTixLQUFLO1lBQ0gsT0FBTzdCLE9BQU9LLGlCQUFpQjtRQUNqQyxLQUFLO1lBQ0gsT0FBT0wsT0FBT1EsbUJBQW1CO1FBQ25DLEtBQUs7WUFDSCxPQUFPUixPQUFPSSxxQkFBcUI7UUFDckMsS0FBSztZQUNILE9BQU9KLE9BQU9PLHVCQUF1QjtRQUN2QyxLQUFLO1lBQ0gsT0FBT1AsT0FBT1UscUJBQXFCO1FBQ3JDLEtBQUs7WUFDSCxPQUFPVixPQUFPQyxjQUFjLEdBQUcsR0FBRyxvREFBb0Q7UUFDeEY7WUFDRSxPQUFPO0lBQ1g7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGxpYlxcc3RyaXBlLWNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDbGllbnQtc2FmZSBTdHJpcGUgdXRpbGl0aWVzIChubyBzZXJ2ZXItc2lkZSBTdHJpcGUgaW5zdGFuY2UpXG5pbXBvcnQgeyBTVFJJUEVfUFJJQ0VfSURTLCBTVFJJUEVfUFJPRFVDVF9JRFMgfSBmcm9tICcuL3N0cmlwZS1jb25maWcnO1xuXG5leHBvcnQgdHlwZSBTdWJzY3JpcHRpb25UaWVyID0gJ2ZyZWUnIHwgJ3N0YXJ0ZXInIHwgJ3Byb2Zlc3Npb25hbCcgfCAnZW50ZXJwcmlzZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVGllckNvbmZpZyB7XG4gIG5hbWU6IHN0cmluZztcbiAgcHJpY2U6IHN0cmluZztcbiAgcHJpY2VJZDogc3RyaW5nIHwgbnVsbDsgLy8gbnVsbCBmb3IgZnJlZSB0aWVyXG4gIHByb2R1Y3RJZDogc3RyaW5nIHwgbnVsbDsgLy8gbnVsbCBmb3IgZnJlZSB0aWVyXG4gIGZlYXR1cmVzOiBzdHJpbmdbXTtcbiAgbGltaXRzOiB7XG4gICAgY29uZmlndXJhdGlvbnM6IG51bWJlcjtcbiAgICBhcGlLZXlzUGVyQ29uZmlnOiBudW1iZXI7XG4gICAgYXBpUmVxdWVzdHM6IG51bWJlcjtcbiAgICBjYW5Vc2VBZHZhbmNlZFJvdXRpbmc6IGJvb2xlYW47XG4gICAgY2FuVXNlQ3VzdG9tUm9sZXM6IGJvb2xlYW47XG4gICAgbWF4Q3VzdG9tUm9sZXM6IG51bWJlcjtcbiAgICBjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZzogYm9vbGVhbjtcbiAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiBib29sZWFuO1xuICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IG51bWJlcjtcbiAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IGJvb2xlYW47XG4gIH07XG59XG5cbmV4cG9ydCBjb25zdCBUSUVSX0NPTkZJR1M6IFJlY29yZDxTdWJzY3JpcHRpb25UaWVyLCBUaWVyQ29uZmlnPiA9IHtcbiAgZnJlZToge1xuICAgIG5hbWU6ICdGcmVlJyxcbiAgICBwcmljZTogJyQwJyxcbiAgICBwcmljZUlkOiBTVFJJUEVfUFJJQ0VfSURTLkZSRUUsXG4gICAgcHJvZHVjdElkOiBTVFJJUEVfUFJPRFVDVF9JRFMuRlJFRSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ1VubGltaXRlZCBBUEkgcmVxdWVzdHMnLFxuICAgICAgJzEgQ3VzdG9tIENvbmZpZ3VyYXRpb24nLFxuICAgICAgJzMgQVBJIEtleXMgcGVyIGNvbmZpZycsXG4gICAgICAnQWxsIDMwMCsgQUkgbW9kZWxzJyxcbiAgICAgICdTdHJpY3QgZmFsbGJhY2sgcm91dGluZyBvbmx5JyxcbiAgICAgICdCYXNpYyBhbmFseXRpY3Mgb25seScsXG4gICAgICAnTm8gY3VzdG9tIHJvbGVzLCBiYXNpYyByb3V0ZXIgb25seScsXG4gICAgICAnTGltaXRlZCBsb2dzJyxcbiAgICAgICdDb21tdW5pdHkgc3VwcG9ydCcsXG4gICAgXSxcbiAgICBsaW1pdHM6IHtcbiAgICAgIGNvbmZpZ3VyYXRpb25zOiAxLFxuICAgICAgYXBpS2V5c1BlckNvbmZpZzogMyxcbiAgICAgIGFwaVJlcXVlc3RzOiA5OTk5OTksXG4gICAgICBjYW5Vc2VBZHZhbmNlZFJvdXRpbmc6IGZhbHNlLFxuICAgICAgY2FuVXNlQ3VzdG9tUm9sZXM6IGZhbHNlLFxuICAgICAgbWF4Q3VzdG9tUm9sZXM6IDAsXG4gICAgICBjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZzogZmFsc2UsXG4gICAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiBmYWxzZSxcbiAgICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IDAsXG4gICAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IGZhbHNlLFxuICAgIH0sXG4gIH0sXG4gIHN0YXJ0ZXI6IHtcbiAgICBuYW1lOiAnU3RhcnRlcicsXG4gICAgcHJpY2U6ICckMTknLFxuICAgIHByaWNlSWQ6IFNUUklQRV9QUklDRV9JRFMuU1RBUlRFUixcbiAgICBwcm9kdWN0SWQ6IFNUUklQRV9QUk9EVUNUX0lEUy5TVEFSVEVSLFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnVW5saW1pdGVkIEFQSSByZXF1ZXN0cycsXG4gICAgICAnNCBDdXN0b20gQ29uZmlndXJhdGlvbnMnLFxuICAgICAgJzUgQVBJIEtleXMgcGVyIGNvbmZpZycsXG4gICAgICAnQWxsIDMwMCsgQUkgbW9kZWxzJyxcbiAgICAgICdTdHJpY3QgZmFsbGJhY2sgKyBDb21wbGV4IHJvdXRpbmcgKDEgY29uZmlnIGxpbWl0KScsXG4gICAgICAnVXAgdG8gMyBjdXN0b20gcm9sZXMnLFxuICAgICAgJ0ludGVsbGlnZW50IHJvbGUgcm91dGluZyAoMSBjb25maWcpJyxcbiAgICAgICdQcm9tcHQgZW5naW5lZXJpbmcgKG5vIGZpbGUgdXBsb2FkKScsXG4gICAgICAnRW5oYW5jZWQgbG9ncyBhbmQgYW5hbHl0aWNzJyxcbiAgICAgICdDb21tdW5pdHkgc3VwcG9ydCcsXG4gICAgXSxcbiAgICBsaW1pdHM6IHtcbiAgICAgIGNvbmZpZ3VyYXRpb25zOiA0LFxuICAgICAgYXBpS2V5c1BlckNvbmZpZzogNSxcbiAgICAgIGFwaVJlcXVlc3RzOiA5OTk5OTksXG4gICAgICBjYW5Vc2VBZHZhbmNlZFJvdXRpbmc6IHRydWUsXG4gICAgICBjYW5Vc2VDdXN0b21Sb2xlczogdHJ1ZSxcbiAgICAgIG1heEN1c3RvbVJvbGVzOiAzLFxuICAgICAgY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc6IHRydWUsXG4gICAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiBmYWxzZSxcbiAgICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IDAsXG4gICAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IGZhbHNlLFxuICAgIH0sXG4gIH0sXG4gIHByb2Zlc3Npb25hbDoge1xuICAgIG5hbWU6ICdQcm9mZXNzaW9uYWwnLFxuICAgIHByaWNlOiAnJDQ5JyxcbiAgICBwcmljZUlkOiBTVFJJUEVfUFJJQ0VfSURTLlBST0ZFU1NJT05BTCxcbiAgICBwcm9kdWN0SWQ6IFNUUklQRV9QUk9EVUNUX0lEUy5QUk9GRVNTSU9OQUwsXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICdVbmxpbWl0ZWQgQVBJIHJlcXVlc3RzJyxcbiAgICAgICcyMCBDdXN0b20gQ29uZmlndXJhdGlvbnMnLFxuICAgICAgJzE1IEFQSSBLZXlzIHBlciBjb25maWcnLFxuICAgICAgJ0FsbCAzMDArIEFJIG1vZGVscycsXG4gICAgICAnQWxsIGFkdmFuY2VkIHJvdXRpbmcgc3RyYXRlZ2llcycsXG4gICAgICAnVW5saW1pdGVkIGN1c3RvbSByb2xlcycsXG4gICAgICAnUHJvbXB0IGVuZ2luZWVyaW5nICsgS25vd2xlZGdlIGJhc2UgKDUgZG9jdW1lbnRzKScsXG4gICAgICAnU2VtYW50aWMgY2FjaGluZycsXG4gICAgICAnQWR2YW5jZWQgYW5hbHl0aWNzIGFuZCBsb2dnaW5nJyxcbiAgICAgICdQcmlvcml0eSBlbWFpbCBzdXBwb3J0JyxcbiAgICBdLFxuICAgIGxpbWl0czoge1xuICAgICAgY29uZmlndXJhdGlvbnM6IDIwLFxuICAgICAgYXBpS2V5c1BlckNvbmZpZzogMTUsXG4gICAgICBhcGlSZXF1ZXN0czogOTk5OTk5LFxuICAgICAgY2FuVXNlQWR2YW5jZWRSb3V0aW5nOiB0cnVlLFxuICAgICAgY2FuVXNlQ3VzdG9tUm9sZXM6IHRydWUsXG4gICAgICBtYXhDdXN0b21Sb2xlczogOTk5OTk5LFxuICAgICAgY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc6IHRydWUsXG4gICAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiB0cnVlLFxuICAgICAga25vd2xlZGdlQmFzZURvY3VtZW50czogNSxcbiAgICAgIGNhblVzZVNlbWFudGljQ2FjaGluZzogdHJ1ZSxcbiAgICB9LFxuICB9LFxuICBlbnRlcnByaXNlOiB7XG4gICAgbmFtZTogJ0VudGVycHJpc2UnLFxuICAgIHByaWNlOiAnJDE0OScsXG4gICAgcHJpY2VJZDogU1RSSVBFX1BSSUNFX0lEUy5FTlRFUlBSSVNFLFxuICAgIHByb2R1Y3RJZDogU1RSSVBFX1BST0RVQ1RfSURTLkVOVEVSUFJJU0UsXG4gICAgZmVhdHVyZXM6IFtcbiAgICAgICdVbmxpbWl0ZWQgQVBJIHJlcXVlc3RzJyxcbiAgICAgICdVbmxpbWl0ZWQgY29uZmlndXJhdGlvbnMnLFxuICAgICAgJ1VubGltaXRlZCBBUEkga2V5cycsXG4gICAgICAnQWxsIDMwMCsgbW9kZWxzICsgcHJpb3JpdHkgYWNjZXNzJyxcbiAgICAgICdBbGwgcm91dGluZyBzdHJhdGVnaWVzJyxcbiAgICAgICdVbmxpbWl0ZWQgY3VzdG9tIHJvbGVzJyxcbiAgICAgICdBbGwgZmVhdHVyZXMgKyBwcmlvcml0eSBzdXBwb3J0JyxcbiAgICAgICdVbmxpbWl0ZWQga25vd2xlZGdlIGJhc2UgZG9jdW1lbnRzJyxcbiAgICAgICdBZHZhbmNlZCBzZW1hbnRpYyBjYWNoaW5nJyxcbiAgICAgICdDdXN0b20gaW50ZWdyYXRpb25zJyxcbiAgICAgICdEZWRpY2F0ZWQgc3VwcG9ydCArIHBob25lJyxcbiAgICAgICdTTEEgZ3VhcmFudGVlJyxcbiAgICBdLFxuICAgIGxpbWl0czoge1xuICAgICAgY29uZmlndXJhdGlvbnM6IDk5OTk5OSxcbiAgICAgIGFwaUtleXNQZXJDb25maWc6IDk5OTk5OSxcbiAgICAgIGFwaVJlcXVlc3RzOiA5OTk5OTksXG4gICAgICBjYW5Vc2VBZHZhbmNlZFJvdXRpbmc6IHRydWUsXG4gICAgICBjYW5Vc2VDdXN0b21Sb2xlczogdHJ1ZSxcbiAgICAgIG1heEN1c3RvbVJvbGVzOiA5OTk5OTksXG4gICAgICBjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZzogdHJ1ZSxcbiAgICAgIGNhblVzZUtub3dsZWRnZUJhc2U6IHRydWUsXG4gICAgICBrbm93bGVkZ2VCYXNlRG9jdW1lbnRzOiA5OTk5OTksXG4gICAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IHRydWUsXG4gICAgfSxcbiAgfSxcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRUaWVyQ29uZmlnKHRpZXI6IFN1YnNjcmlwdGlvblRpZXIpOiBUaWVyQ29uZmlnIHtcbiAgcmV0dXJuIFRJRVJfQ09ORklHU1t0aWVyXTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFByaWNlSWRGb3JUaWVyKHRpZXI6IFN1YnNjcmlwdGlvblRpZXIpOiBzdHJpbmcgfCBudWxsIHtcbiAgcmV0dXJuIFRJRVJfQ09ORklHU1t0aWVyXS5wcmljZUlkO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0VGllckZyb21QcmljZUlkKHByaWNlSWQ6IHN0cmluZyk6IFN1YnNjcmlwdGlvblRpZXIge1xuICBmb3IgKGNvbnN0IFt0aWVyLCBjb25maWddIG9mIE9iamVjdC5lbnRyaWVzKFRJRVJfQ09ORklHUykpIHtcbiAgICBpZiAoY29uZmlnLnByaWNlSWQgPT09IHByaWNlSWQpIHtcbiAgICAgIHJldHVybiB0aWVyIGFzIFN1YnNjcmlwdGlvblRpZXI7XG4gICAgfVxuICB9XG4gIHJldHVybiAnZnJlZSc7IC8vIERlZmF1bHQgZmFsbGJhY2sgdG8gZnJlZSB0aWVyXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBmb3JtYXRQcmljZSh0aWVyOiBTdWJzY3JpcHRpb25UaWVyKTogc3RyaW5nIHtcbiAgcmV0dXJuIFRJRVJfQ09ORklHU1t0aWVyXS5wcmljZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGNhblBlcmZvcm1BY3Rpb24oXG4gIHRpZXI6IFN1YnNjcmlwdGlvblRpZXIsXG4gIGFjdGlvbjogJ2NyZWF0ZV9jb25maWcnIHwgJ2NyZWF0ZV9hcGlfa2V5JyxcbiAgY3VycmVudENvdW50OiBudW1iZXJcbik6IGJvb2xlYW4ge1xuICBjb25zdCBsaW1pdHMgPSBUSUVSX0NPTkZJR1NbdGllcl0ubGltaXRzO1xuXG4gIHN3aXRjaCAoYWN0aW9uKSB7XG4gICAgY2FzZSAnY3JlYXRlX2NvbmZpZyc6XG4gICAgICByZXR1cm4gY3VycmVudENvdW50IDwgbGltaXRzLmNvbmZpZ3VyYXRpb25zO1xuICAgIGNhc2UgJ2NyZWF0ZV9hcGlfa2V5JzpcbiAgICAgIHJldHVybiBjdXJyZW50Q291bnQgPCBsaW1pdHMuYXBpS2V5c1BlckNvbmZpZztcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIHRydWU7XG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGhhc0ZlYXR1cmVBY2Nlc3MoXG4gIHRpZXI6IFN1YnNjcmlwdGlvblRpZXIsXG4gIGZlYXR1cmU6ICdjdXN0b21fcm9sZXMnIHwgJ2tub3dsZWRnZV9iYXNlJyB8ICdhZHZhbmNlZF9yb3V0aW5nJyB8ICdwcm9tcHRfZW5naW5lZXJpbmcnIHwgJ3NlbWFudGljX2NhY2hpbmcnIHwgJ2NvbmZpZ3VyYXRpb25zJ1xuKTogYm9vbGVhbiB7XG4gIGNvbnN0IGxpbWl0cyA9IFRJRVJfQ09ORklHU1t0aWVyXS5saW1pdHM7XG5cbiAgc3dpdGNoIChmZWF0dXJlKSB7XG4gICAgY2FzZSAnY3VzdG9tX3JvbGVzJzpcbiAgICAgIHJldHVybiBsaW1pdHMuY2FuVXNlQ3VzdG9tUm9sZXM7XG4gICAgY2FzZSAna25vd2xlZGdlX2Jhc2UnOlxuICAgICAgcmV0dXJuIGxpbWl0cy5jYW5Vc2VLbm93bGVkZ2VCYXNlO1xuICAgIGNhc2UgJ2FkdmFuY2VkX3JvdXRpbmcnOlxuICAgICAgcmV0dXJuIGxpbWl0cy5jYW5Vc2VBZHZhbmNlZFJvdXRpbmc7XG4gICAgY2FzZSAncHJvbXB0X2VuZ2luZWVyaW5nJzpcbiAgICAgIHJldHVybiBsaW1pdHMuY2FuVXNlUHJvbXB0RW5naW5lZXJpbmc7XG4gICAgY2FzZSAnc2VtYW50aWNfY2FjaGluZyc6XG4gICAgICByZXR1cm4gbGltaXRzLmNhblVzZVNlbWFudGljQ2FjaGluZztcbiAgICBjYXNlICdjb25maWd1cmF0aW9ucyc6XG4gICAgICByZXR1cm4gbGltaXRzLmNvbmZpZ3VyYXRpb25zID4gMDsgLy8gQWxsIHRpZXJzIGNhbiBjcmVhdGUgYXQgbGVhc3Qgc29tZSBjb25maWd1cmF0aW9uc1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn1cblxuZXhwb3J0IGludGVyZmFjZSBTdWJzY3JpcHRpb25TdGF0dXMge1xuICBoYXNBY3RpdmVTdWJzY3JpcHRpb246IGJvb2xlYW47XG4gIHRpZXI6IFN1YnNjcmlwdGlvblRpZXI7XG4gIHN0YXR1czogc3RyaW5nIHwgbnVsbDtcbiAgY3VycmVudFBlcmlvZEVuZDogc3RyaW5nIHwgbnVsbDtcbiAgY2FuY2VsQXRQZXJpb2RFbmQ6IGJvb2xlYW47XG4gIGlzRnJlZTogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBVc2FnZVN0YXR1cyB7XG4gIHRpZXI6IFN1YnNjcmlwdGlvblRpZXI7XG4gIHVzYWdlOiB7XG4gICAgY29uZmlndXJhdGlvbnM6IG51bWJlcjtcbiAgICBhcGlLZXlzOiBudW1iZXI7XG4gICAgYXBpUmVxdWVzdHM6IG51bWJlcjtcbiAgfTtcbiAgbGltaXRzOiB7XG4gICAgY29uZmlndXJhdGlvbnM6IG51bWJlcjtcbiAgICBhcGlLZXlzUGVyQ29uZmlnOiBudW1iZXI7XG4gICAgYXBpUmVxdWVzdHM6IG51bWJlcjtcbiAgICBjYW5Vc2VBZHZhbmNlZFJvdXRpbmc6IGJvb2xlYW47XG4gICAgY2FuVXNlQ3VzdG9tUm9sZXM6IGJvb2xlYW47XG4gICAgbWF4Q3VzdG9tUm9sZXM6IG51bWJlcjtcbiAgICBjYW5Vc2VQcm9tcHRFbmdpbmVlcmluZzogYm9vbGVhbjtcbiAgICBjYW5Vc2VLbm93bGVkZ2VCYXNlOiBib29sZWFuO1xuICAgIGtub3dsZWRnZUJhc2VEb2N1bWVudHM6IG51bWJlcjtcbiAgICBjYW5Vc2VTZW1hbnRpY0NhY2hpbmc6IGJvb2xlYW47XG4gIH07XG4gIGNhbkNyZWF0ZUNvbmZpZzogYm9vbGVhbjtcbiAgY2FuQ3JlYXRlQXBpS2V5OiBib29sZWFuO1xufVxuIl0sIm5hbWVzIjpbIlNUUklQRV9QUklDRV9JRFMiLCJTVFJJUEVfUFJPRFVDVF9JRFMiLCJUSUVSX0NPTkZJR1MiLCJmcmVlIiwibmFtZSIsInByaWNlIiwicHJpY2VJZCIsIkZSRUUiLCJwcm9kdWN0SWQiLCJmZWF0dXJlcyIsImxpbWl0cyIsImNvbmZpZ3VyYXRpb25zIiwiYXBpS2V5c1BlckNvbmZpZyIsImFwaVJlcXVlc3RzIiwiY2FuVXNlQWR2YW5jZWRSb3V0aW5nIiwiY2FuVXNlQ3VzdG9tUm9sZXMiLCJtYXhDdXN0b21Sb2xlcyIsImNhblVzZVByb21wdEVuZ2luZWVyaW5nIiwiY2FuVXNlS25vd2xlZGdlQmFzZSIsImtub3dsZWRnZUJhc2VEb2N1bWVudHMiLCJjYW5Vc2VTZW1hbnRpY0NhY2hpbmciLCJzdGFydGVyIiwiU1RBUlRFUiIsInByb2Zlc3Npb25hbCIsIlBST0ZFU1NJT05BTCIsImVudGVycHJpc2UiLCJFTlRFUlBSSVNFIiwiZ2V0VGllckNvbmZpZyIsInRpZXIiLCJnZXRQcmljZUlkRm9yVGllciIsImdldFRpZXJGcm9tUHJpY2VJZCIsImNvbmZpZyIsIk9iamVjdCIsImVudHJpZXMiLCJmb3JtYXRQcmljZSIsImNhblBlcmZvcm1BY3Rpb24iLCJhY3Rpb24iLCJjdXJyZW50Q291bnQiLCJoYXNGZWF0dXJlQWNjZXNzIiwiZmVhdHVyZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe-config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe-config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUBLIC_STRIPE_PRICE_IDS: () => (/* binding */ PUBLIC_STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_ENV_INFO: () => (/* binding */ STRIPE_ENV_INFO),\n/* harmony export */   STRIPE_KEYS: () => (/* binding */ STRIPE_KEYS),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   STRIPE_PRODUCT_IDS: () => (/* binding */ STRIPE_PRODUCT_IDS)\n/* harmony export */ });\n// Stripe Configuration with Environment Detection\n// Automatically switches between test and live keys based on environment\nconst isProduction = \"development\" === 'production';\n// Stripe Keys - Auto-selected based on environment\nconst STRIPE_KEYS = {\n    publishableKey: isProduction ? process.env.STRIPE_LIVE_PUBLISHABLE_KEY : process.env.STRIPE_TEST_PUBLISHABLE_KEY,\n    secretKey: isProduction ? process.env.STRIPE_LIVE_SECRET_KEY : process.env.STRIPE_TEST_SECRET_KEY,\n    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET\n};\n// Stripe Price IDs - Auto-selected based on environment\nconst STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Stripe Product IDs - Auto-selected based on environment\nconst STRIPE_PRODUCT_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRODUCT_ID : process.env.STRIPE_TEST_FREE_PRODUCT_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRODUCT_ID : process.env.STRIPE_TEST_STARTER_PRODUCT_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRODUCT_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRODUCT_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRODUCT_ID : process.env.STRIPE_TEST_ENTERPRISE_PRODUCT_ID\n};\n// Public Price IDs for frontend (auto-selected based on environment)\nconst PUBLIC_STRIPE_PRICE_IDS = {\n    FREE: isProduction ? process.env.STRIPE_LIVE_FREE_PRICE_ID : process.env.STRIPE_TEST_FREE_PRICE_ID,\n    STARTER: isProduction ? process.env.STRIPE_LIVE_STARTER_PRICE_ID : process.env.STRIPE_TEST_STARTER_PRICE_ID,\n    PROFESSIONAL: isProduction ? process.env.STRIPE_LIVE_PROFESSIONAL_PRICE_ID : process.env.STRIPE_TEST_PROFESSIONAL_PRICE_ID,\n    ENTERPRISE: isProduction ? process.env.STRIPE_LIVE_ENTERPRISE_PRICE_ID : process.env.STRIPE_TEST_ENTERPRISE_PRICE_ID\n};\n// Environment info for debugging\nconst STRIPE_ENV_INFO = {\n    isProduction,\n    environment: isProduction ? 'LIVE' : 'TEST',\n    keysUsed: {\n        publishable: STRIPE_KEYS.publishableKey ? STRIPE_KEYS.publishableKey.substring(0, 20) + '...' : 'undefined',\n        secret: STRIPE_KEYS.secretKey ? STRIPE_KEYS.secretKey.substring(0, 20) + '...' : 'undefined'\n    }\n};\n// Log environment info in development\nif (!isProduction) {\n    console.log('🔧 Stripe Environment:', STRIPE_ENV_INFO.environment);\n    console.log('🔑 Using keys:', STRIPE_ENV_INFO.keysUsed);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe-config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftraining%2Fjobs%2Froute&page=%2Fapi%2Ftraining%2Fjobs%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftraining%2Fjobs%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();