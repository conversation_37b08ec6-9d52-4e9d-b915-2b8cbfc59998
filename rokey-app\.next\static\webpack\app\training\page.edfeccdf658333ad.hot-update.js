"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/DocumentUpload.tsx":
/*!*******************************************!*\
  !*** ./src/components/DocumentUpload.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,File,FileText,Loader2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DocumentUpload(param) {\n    let { configId, onDocumentUploaded } = param;\n    _s();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshing, setIsRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [dragActive, setDragActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation)();\n    // Load documents for the current config with retry logic\n    const loadDocuments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[loadDocuments]\": async function() {\n            let retryCount = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, expectedDocumentId = arguments.length > 1 ? arguments[1] : void 0;\n            if (!configId) return;\n            if (retryCount === 0) {\n                setIsRefreshing(true);\n            }\n            try {\n                const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n                if (response.ok) {\n                    const data = await response.json();\n                    const newDocuments = data.documents || [];\n                    // If we're looking for a specific document and it's not found, retry\n                    if (expectedDocumentId && retryCount < 3) {\n                        const foundDocument = newDocuments.find({\n                            \"DocumentUpload.useCallback[loadDocuments].foundDocument\": (doc)=>doc.id === expectedDocumentId\n                        }[\"DocumentUpload.useCallback[loadDocuments].foundDocument\"]);\n                        if (!foundDocument) {\n                            console.log(\"[DocumentUpload] Document \".concat(expectedDocumentId, \" not found, retrying in \").concat((retryCount + 1) * 500, \"ms...\"));\n                            setTimeout({\n                                \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                                    loadDocuments(retryCount + 1, expectedDocumentId);\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"], (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays\n                            return;\n                        }\n                    }\n                    // Merge with existing documents, avoiding duplicates\n                    setDocuments({\n                        \"DocumentUpload.useCallback[loadDocuments]\": (prev)=>{\n                            const existingIds = new Set(prev.map({\n                                \"DocumentUpload.useCallback[loadDocuments]\": (doc)=>doc.id\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"]));\n                            const uniqueNewDocs = newDocuments.filter({\n                                \"DocumentUpload.useCallback[loadDocuments].uniqueNewDocs\": (doc)=>!existingIds.has(doc.id)\n                            }[\"DocumentUpload.useCallback[loadDocuments].uniqueNewDocs\"]);\n                            const updatedExistingDocs = prev.map({\n                                \"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs\": (doc)=>{\n                                    const serverDoc = newDocuments.find({\n                                        \"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs.serverDoc\": (d)=>d.id === doc.id\n                                    }[\"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs.serverDoc\"]);\n                                    return serverDoc || doc; // Use server data if available, otherwise keep existing\n                                }\n                            }[\"DocumentUpload.useCallback[loadDocuments].updatedExistingDocs\"]);\n                            // Combine updated existing docs with unique new docs, sorted by creation date\n                            const allDocs = [\n                                ...updatedExistingDocs,\n                                ...uniqueNewDocs\n                            ];\n                            return allDocs.sort({\n                                \"DocumentUpload.useCallback[loadDocuments]\": (a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime()\n                            }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"]);\n                }\n            } catch (err) {\n                console.error('Failed to load documents:', err);\n                // Retry on error if we haven't exceeded retry count\n                if (retryCount < 2) {\n                    setTimeout({\n                        \"DocumentUpload.useCallback[loadDocuments]\": ()=>{\n                            loadDocuments(retryCount + 1, expectedDocumentId);\n                        }\n                    }[\"DocumentUpload.useCallback[loadDocuments]\"], 1000);\n                }\n            } finally{\n                if (retryCount === 0 || expectedDocumentId) {\n                    setIsRefreshing(false);\n                }\n            }\n        }\n    }[\"DocumentUpload.useCallback[loadDocuments]\"], [\n        configId\n    ]);\n    // Load documents when configId changes\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"DocumentUpload.useEffect\": ()=>{\n            loadDocuments();\n        }\n    }[\"DocumentUpload.useEffect\"], [\n        loadDocuments\n    ]);\n    // Handle file upload\n    const handleFileUpload = async (files)=>{\n        if (!configId) {\n            setError('Please select an API configuration first');\n            return;\n        }\n        const file = files[0];\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            'application/pdf',\n            'text/plain',\n            'text/markdown'\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError('Please upload PDF, TXT, or MD files only');\n            return;\n        }\n        // Validate file size (10MB max)\n        if (file.size > 10 * 1024 * 1024) {\n            setError('File size must be less than 10MB');\n            return;\n        }\n        setIsUploading(true);\n        setError(null);\n        setSuccess(null);\n        setUploadProgress(0);\n        try {\n            const formData = new FormData();\n            formData.append('file', file);\n            formData.append('configId', configId);\n            // Simulate progress for better UX\n            const progressInterval = setInterval(()=>{\n                setUploadProgress((prev)=>Math.min(prev + 10, 90));\n            }, 200);\n            const response = await fetch('/api/documents/upload', {\n                method: 'POST',\n                body: formData\n            });\n            clearInterval(progressInterval);\n            setUploadProgress(100);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Upload failed');\n            }\n            const result = await response.json();\n            console.log(\"[DocumentUpload] Upload successful:\", result);\n            setSuccess(\"✨ \".concat(file.name, \" uploaded successfully! Processing \").concat(result.document.chunks_total, \" chunks.\"));\n            // Optimistically add the document to the list immediately\n            const optimisticDocument = {\n                id: result.document.id,\n                filename: result.document.filename || file.name,\n                file_type: file.type,\n                file_size: file.size,\n                status: result.document.status || 'processing',\n                chunks_count: result.document.chunks_processed || 0,\n                created_at: new Date().toISOString()\n            };\n            console.log(\"[DocumentUpload] Adding optimistic document:\", optimisticDocument);\n            setDocuments((prev)=>{\n                // Check if document already exists (shouldn't happen, but safety check)\n                const exists = prev.find((doc)=>doc.id === optimisticDocument.id);\n                if (exists) {\n                    console.log(\"[DocumentUpload] Document \".concat(optimisticDocument.id, \" already exists, updating instead\"));\n                    return prev.map((doc)=>doc.id === optimisticDocument.id ? optimisticDocument : doc);\n                }\n                return [\n                    optimisticDocument,\n                    ...prev\n                ];\n            });\n            // Small delay to allow database transaction to commit, then reload with retry logic\n            setTimeout(async ()=>{\n                console.log(\"[DocumentUpload] Starting document list refresh for document: \".concat(result.document.id));\n                await loadDocuments(0, result.document.id);\n            }, 200);\n            // Call callback if provided\n            onDocumentUploaded === null || onDocumentUploaded === void 0 ? void 0 : onDocumentUploaded();\n        } catch (err) {\n            const errorMessage = \"Upload failed: \".concat(err.message);\n            setError(errorMessage);\n            // Auto-clear error message after 8 seconds\n            setTimeout(()=>setError(null), 8000);\n        } finally{\n            setIsUploading(false);\n            setUploadProgress(0);\n            // Clear file input\n            if (fileInputRef.current) {\n                fileInputRef.current.value = '';\n            }\n            // Auto-clear success message after 5 seconds\n            if (success) {\n                setTimeout(()=>setSuccess(null), 5000);\n            }\n        }\n    };\n    // Handle drag and drop\n    const handleDrag = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrag]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            if (e.type === 'dragenter' || e.type === 'dragover') {\n                setDragActive(true);\n            } else if (e.type === 'dragleave') {\n                setDragActive(false);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrag]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentUpload.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            setDragActive(false);\n            if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n                handleFileUpload(e.dataTransfer.files);\n            }\n        }\n    }[\"DocumentUpload.useCallback[handleDrop]\"], [\n        configId\n    ]);\n    // Handle file input change\n    const handleInputChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            handleFileUpload(e.target.files);\n        }\n    };\n    // Delete document\n    const handleDeleteDocument = (documentId, documentName)=>{\n        confirmation.showConfirmation({\n            title: 'Delete Document',\n            message: 'Are you sure you want to delete \"'.concat(documentName, '\"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.'),\n            confirmText: 'Delete Document',\n            cancelText: 'Cancel',\n            type: 'danger'\n        }, async ()=>{\n            // Optimistically remove the document from the list\n            const originalDocuments = documents;\n            setDocuments((prev)=>prev.filter((doc)=>doc.id !== documentId));\n            try {\n                const response = await fetch(\"/api/documents/\".concat(documentId), {\n                    method: 'DELETE'\n                });\n                if (!response.ok) {\n                    // Restore the original list on error\n                    setDocuments(originalDocuments);\n                    throw new Error('Failed to delete document');\n                }\n                setSuccess('Document deleted successfully');\n                // Auto-clear success message after 3 seconds\n                setTimeout(()=>setSuccess(null), 3000);\n            } catch (err) {\n                // Restore the original list on error\n                setDocuments(originalDocuments);\n                const errorMessage = \"Delete failed: \".concat(err.message);\n                setError(errorMessage);\n                // Auto-clear error message after 8 seconds\n                setTimeout(()=>setError(null), 8000);\n            }\n        });\n    };\n    // Format file size\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    // Get file icon\n    const getFileIcon = (fileType)=>{\n        if (fileType.includes('pdf')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-red-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 293,\n            columnNumber: 42\n        }, this);\n        if (fileType.includes('word')) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-blue-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 294,\n            columnNumber: 43\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-gray-500\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n            lineNumber: 295,\n            columnNumber: 12\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-800 text-sm font-medium\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-xl p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-5 h-5 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-green-800 text-sm font-medium\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 311,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform \".concat(dragActive ? 'border-orange-400 bg-orange-50 scale-105 shadow-lg' : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md', \" \").concat(!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                onDragEnter: handleDrag,\n                onDragLeave: handleDrag,\n                onDragOver: handleDrag,\n                onDrop: handleDrop,\n                onClick: ()=>{\n                    var _fileInputRef_current;\n                    return configId && ((_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click());\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        className: \"hidden\",\n                        accept: \".pdf,.txt,.md\",\n                        onChange: handleInputChange,\n                        disabled: !configId || isUploading\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-12 h-12 text-orange-500 mx-auto animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: \"Processing Document...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(uploadProgress, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            uploadProgress,\n                                            \"% complete\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium text-gray-900\",\n                                        children: configId ? 'Upload Knowledge Documents' : 'Select a configuration first'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mt-1\",\n                                        children: \"Drag and drop files here, or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-2\",\n                                        children: \"Supports PDF, TXT, MD files up to 10MB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            documents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900\",\n                                children: \"Uploaded Documents\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this),\n                            isRefreshing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Refreshing...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-4\",\n                        children: documents.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-white border border-gray-200 rounded-xl hover:shadow-md transition-shadow\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            getFileIcon(doc.file_type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: doc.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            formatFileSize(doc.file_size),\n                                                            \" • \",\n                                                            doc.chunks_count,\n                                                            \" chunks\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    doc.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-5 h-5 text-orange-500 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    doc.status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium \".concat(doc.status === 'completed' ? 'text-green-600' : doc.status === 'processing' ? 'text-orange-600' : 'text-red-600'),\n                                                        children: doc.status === 'completed' ? 'Ready' : doc.status === 'processing' ? 'Processing' : 'Failed'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteDocument(doc.id),\n                                                className: \"p-1 text-gray-400 hover:text-red-500 transition-colors\",\n                                                title: \"Delete document\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_File_FileText_Loader2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, doc.id, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n                lineNumber: 375,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DocumentUpload.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n_s(DocumentUpload, \"qy7mnAkmgJ7Ofmiub+gVBhPvEic=\", false, function() {\n    return [\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_2__.useConfirmation\n    ];\n});\n_c = DocumentUpload;\nvar _c;\n$RefreshReg$(_c, \"DocumentUpload\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentUpload.tsx\n"));

/***/ })

});