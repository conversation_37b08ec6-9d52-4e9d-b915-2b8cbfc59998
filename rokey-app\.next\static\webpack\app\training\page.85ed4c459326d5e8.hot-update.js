"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/app/training/page.tsx":
/*!***********************************!*\
  !*** ./src/app/training/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrainingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/DocumentUpload */ \"(app-pages-browser)/./src/components/DocumentUpload.tsx\");\n/* harmony import */ var _components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/TierEnforcement */ \"(app-pages-browser)/./src/components/TierEnforcement/index.ts\");\n/* harmony import */ var _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSubscription */ \"(app-pages-browser)/./src/hooks/useSubscription.ts\");\n/* harmony import */ var _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useConfirmation */ \"(app-pages-browser)/./src/hooks/useConfirmation.ts\");\n/* harmony import */ var _components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/ConfirmationModal */ \"(app-pages-browser)/./src/components/ui/ConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction TrainingPage() {\n    _s();\n    // Subscription hook\n    const { subscriptionStatus } = (0,_hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription)();\n    const confirmation = (0,_hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation)();\n    // State management\n    const [customConfigs, setCustomConfigs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedConfigId, setSelectedConfigId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [trainingJobs, setTrainingJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentCount, setDocumentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Prompt engineering form state\n    const [trainingPrompts, setTrainingPrompts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Load existing training data for a configuration\n    const loadExistingTrainingData = async (configId)=>{\n        if (!configId) return;\n        try {\n            // Load training jobs\n            const jobsResponse = await fetch(\"/api/training/jobs?custom_api_config_id=\".concat(configId));\n            if (jobsResponse.ok) {\n                const jobs = await jobsResponse.json();\n                if (jobs.length > 0) {\n                    var _latestJob_training_data;\n                    const latestJob = jobs[0];\n                    // Load training prompts\n                    if ((_latestJob_training_data = latestJob.training_data) === null || _latestJob_training_data === void 0 ? void 0 : _latestJob_training_data.raw_prompts) {\n                        setTrainingPrompts(latestJob.training_data.raw_prompts);\n                    }\n                }\n            }\n        } catch (err) {\n            console.warn('Failed to load existing training data:', err);\n        }\n    };\n    // Fetch custom API configs on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            const fetchConfigs = {\n                \"TrainingPage.useEffect.fetchConfigs\": async ()=>{\n                    try {\n                        const response = await fetch('/api/custom-configs');\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch configurations');\n                        }\n                        const data = await response.json();\n                        setCustomConfigs(data);\n                        if (data.length > 0) {\n                            setSelectedConfigId(data[0].id);\n                            loadExistingTrainingData(data[0].id);\n                        }\n                    } catch (err) {\n                        setError(\"Failed to load configurations: \".concat(err.message));\n                    }\n                }\n            }[\"TrainingPage.useEffect.fetchConfigs\"];\n            fetchConfigs();\n        }\n    }[\"TrainingPage.useEffect\"], []);\n    // Load training data when configuration changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                loadExistingTrainingData(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Fetch document count for a configuration\n    const fetchDocumentCount = async (configId)=>{\n        try {\n            const response = await fetch(\"/api/documents/list?configId=\".concat(configId));\n            if (response.ok) {\n                var _data_documents;\n                const data = await response.json();\n                setDocumentCount(((_data_documents = data.documents) === null || _data_documents === void 0 ? void 0 : _data_documents.length) || 0);\n            }\n        } catch (err) {\n            console.error('Error fetching document count:', err);\n        }\n    };\n    // Fetch document count when config changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TrainingPage.useEffect\": ()=>{\n            if (selectedConfigId) {\n                fetchDocumentCount(selectedConfigId);\n            }\n        }\n    }[\"TrainingPage.useEffect\"], [\n        selectedConfigId\n    ]);\n    // Process training prompts into structured format\n    const processTrainingPrompts = (prompts)=>{\n        const processed = {\n            system_instructions: '',\n            examples: [],\n            behavior_guidelines: '',\n            general_instructions: ''\n        };\n        const lines = prompts.split('\\n').filter((line)=>line.trim());\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            if (trimmedLine.startsWith('SYSTEM:')) {\n                processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\\n';\n            } else if (trimmedLine.startsWith('BEHAVIOR:')) {\n                processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\\n';\n            } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {\n                // Parse example: \"User input → Expected response\"\n                const separator = trimmedLine.includes('→') ? '→' : '->';\n                const parts = trimmedLine.split(separator);\n                if (parts.length >= 2) {\n                    const input = parts[0].trim();\n                    const output = parts.slice(1).join(separator).trim();\n                    processed.examples.push({\n                        input,\n                        output\n                    });\n                }\n            } else if (trimmedLine.length > 0) {\n                // General instruction\n                processed.general_instructions += trimmedLine + '\\n';\n            }\n        }\n        return processed;\n    };\n    // Handle training job creation or update\n    const handleStartTraining = async ()=>{\n        if (!selectedConfigId || !trainingPrompts.trim()) {\n            setError('Please select an API configuration and provide training prompts.');\n            return;\n        }\n        // Prevent multiple simultaneous training operations\n        if (isLoading) {\n            console.warn('[Training] Operation already in progress, ignoring duplicate request');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        try {\n            var _customConfigs_find;\n            // Process training prompts\n            const processedPrompts = processTrainingPrompts(trainingPrompts);\n            // Generate a meaningful name based on content\n            const configName = ((_customConfigs_find = customConfigs.find((c)=>c.id === selectedConfigId)) === null || _customConfigs_find === void 0 ? void 0 : _customConfigs_find.name) || 'Unknown Config';\n            // Prepare training job data\n            const trainingJobData = {\n                custom_api_config_id: selectedConfigId,\n                name: \"\".concat(configName, \" Training - \").concat(new Date().toLocaleDateString()),\n                description: \"Training job for \".concat(configName, \" with \").concat(processedPrompts.examples.length, \" examples\"),\n                training_data: {\n                    processed_prompts: processedPrompts,\n                    raw_prompts: trainingPrompts.trim(),\n                    last_prompt_update: new Date().toISOString()\n                },\n                parameters: {\n                    training_type: 'prompt_engineering',\n                    created_via: 'training_page',\n                    version: '1.0'\n                }\n            };\n            // Use UPSERT to handle both create and update scenarios\n            const response = await fetch('/api/training/jobs/upsert', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(trainingJobData)\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('[Training] Failed to upsert training job:', errorText);\n                throw new Error(\"Failed to save training job: \".concat(response.status, \" \").concat(errorText));\n            }\n            const result = await response.json();\n            const isUpdate = result.operation === 'updated';\n            console.log(\"[Training] Successfully \".concat(isUpdate ? 'updated' : 'created', \" training job:\"), result.id);\n            // Show success message based on operation type\n            const operationText = isUpdate ? 'updated' : 'enhanced';\n            const operationEmoji = isUpdate ? '🔄' : '🎉';\n            const successMessage = \"\".concat(operationEmoji, \" Prompt Engineering \").concat(isUpdate ? 'updated' : 'completed', \" successfully!\\n\\n\") + 'Your \"'.concat(configName, '\" configuration has been ').concat(operationText, \" with:\\n\") + \"• \".concat(processedPrompts.examples.length, \" training examples\\n\") + \"• Custom system instructions and behavior guidelines\\n\" + \"\\n✨ All future chats using this configuration will automatically:\\n\" + \"• Follow your training examples\\n\" + \"• Apply your behavior guidelines\\n\" + \"• Maintain consistent personality and responses\\n\\n\" + \"\\uD83D\\uDE80 Try it now in the Playground to see your \".concat(isUpdate ? 'updated' : 'enhanced', \" model in action!\\n\\n\") + \"\\uD83D\\uDCA1 Your training prompts remain here so you can modify them anytime.\";\n            setSuccessMessage(successMessage);\n        } catch (err) {\n            console.error('Error in training operation:', err);\n            setError(\"Failed to create prompt engineering: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#faf8f5] p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-gray-900\",\n                                    children: \"AI Training & Enhancement\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                subscriptionStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierBadge, {\n                                    tier: subscriptionStatus.tier,\n                                    size: \"lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-3xl\",\n                            children: \"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-red-50 border border-red-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm font-medium\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6 bg-green-50 border border-green-200 rounded-xl p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-5 h-5 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm font-medium\",\n                                children: successMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"knowledge_base\",\n                    customMessage: \"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-orange-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Knowledge Documents\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Upload documents to enhance your AI with proprietary knowledge\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            subscriptionStatus && selectedConfigId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.LimitIndicator, {\n                                    current: documentCount,\n                                    limit: subscriptionStatus.tier === 'professional' ? 5 : subscriptionStatus.tier === 'enterprise' ? 999999 : 0,\n                                    label: \"Knowledge Base Documents\",\n                                    tier: subscriptionStatus.tier,\n                                    showUpgradeHint: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DocumentUpload__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                configId: selectedConfigId,\n                                onDocumentUploaded: ()=>{\n                                    // Optionally refresh something or show a message\n                                    console.log('Document uploaded successfully');\n                                    // Refresh document count\n                                    if (selectedConfigId) {\n                                        fetchDocumentCount(selectedConfigId);\n                                    }\n                                },\n                                onDocumentDeleted: ()=>{\n                                    console.log('Document deleted successfully');\n                                    // Refresh document count\n                                    if (selectedConfigId) {\n                                        fetchDocumentCount(selectedConfigId);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TierEnforcement__WEBPACK_IMPORTED_MODULE_3__.TierGuard, {\n                    feature: \"prompt_engineering\",\n                    customMessage: \"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-2xl shadow-lg p-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-blue-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: \"Custom Prompts\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Define behavior, examples, and instructions for your AI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"configSelect\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Select API Configuration\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                id: \"configSelect\",\n                                                value: selectedConfigId,\n                                                onChange: (e)=>setSelectedConfigId(e.target.value),\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Choose which model to train...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    customConfigs.map((config)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: config.id,\n                                                            children: config.name\n                                                        }, config.id, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"trainingPrompts\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Custom Prompts & Instructions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"trainingPrompts\",\n                                                value: trainingPrompts,\n                                                onChange: (e)=>setTrainingPrompts(e.target.value),\n                                                placeholder: \"Enter your training prompts using these formats:\\n\\nSYSTEM: You are a helpful customer service agent for our company\\nBEHAVIOR: Always be polite and offer solutions\\n\\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\\n\\nGeneral instructions can be written as regular text.\",\n                                                rows: 12,\n                                                className: \"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-blue-900 mb-2\",\n                                                        children: \"Training Format Guide:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"text-xs text-blue-800 space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"SYSTEM:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 380,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Core instructions for the AI's role and personality\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"BEHAVIOR:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Guidelines for how the AI should behave\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Examples:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 382,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    ' Use \"User input → Expected response\" format'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"General:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \" Any other instructions written as normal text\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center pt-6 border-t border-gray-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"btn-secondary\",\n                                                    onClick: ()=>{\n                                                        confirmation.showConfirmation({\n                                                            title: 'Clear Training Prompts',\n                                                            message: 'Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.',\n                                                            confirmText: 'Clear All',\n                                                            cancelText: 'Cancel',\n                                                            type: 'warning'\n                                                        }, ()=>{\n                                                            setTrainingPrompts('');\n                                                        });\n                                                    },\n                                                    children: \"Clear Form\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleStartTraining,\n                                                disabled: !selectedConfigId || !trainingPrompts.trim() || isLoading,\n                                                className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Processing Prompts...\"\n                                                    ]\n                                                }, void 0, true) : 'Save Prompts'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: confirmation.isOpen,\n                    onClose: confirmation.hideConfirmation,\n                    onConfirm: confirmation.onConfirm,\n                    title: confirmation.title,\n                    message: confirmation.message,\n                    confirmText: confirmation.confirmText,\n                    cancelText: confirmation.cancelText,\n                    type: confirmation.type,\n                    isLoading: confirmation.isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n            lineNumber: 227,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, this);\n}\n_s(TrainingPage, \"UrnJmCfw8iCy300JBOY9o5vKe/8=\", false, function() {\n    return [\n        _hooks_useSubscription__WEBPACK_IMPORTED_MODULE_4__.useSubscription,\n        _hooks_useConfirmation__WEBPACK_IMPORTED_MODULE_5__.useConfirmation\n    ];\n});\n_c = TrainingPage;\nvar _c;\n$RefreshReg$(_c, \"TrainingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/training/page.tsx\n"));

/***/ })

});