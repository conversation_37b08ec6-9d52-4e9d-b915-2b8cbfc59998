"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/training/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/LimitIndicator.tsx":
/*!***********************************************************!*\
  !*** ./src/components/TierEnforcement/LimitIndicator.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LimitIndicator: () => (/* binding */ LimitIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ LimitIndicator auto */ \n\n\n\nfunction LimitIndicator(param) {\n    let { current, limit, label, tier, showUpgradeHint = true, className = '' } = param;\n    const isUnlimited = limit >= 999999;\n    const percentage = isUnlimited ? 0 : current / limit * 100;\n    const isNearLimit = percentage >= 80;\n    const isAtLimit = current >= limit && !isUnlimited;\n    const getStatusColor = ()=>{\n        if (isUnlimited) return 'text-green-600';\n        if (isAtLimit) return 'text-red-600';\n        if (isNearLimit) return 'text-yellow-600';\n        return 'text-green-600';\n    };\n    const getStatusIcon = ()=>{\n        if (isUnlimited) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 39,\n            columnNumber: 29\n        }, this);\n        if (isAtLimit) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-red-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 40,\n            columnNumber: 27\n        }, this);\n        if (isNearLimit) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-yellow-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 41,\n            columnNumber: 29\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 42,\n            columnNumber: 12\n        }, this);\n    };\n    const getProgressBarColor = ()=>{\n        if (isUnlimited) return 'bg-green-500';\n        if (isAtLimit) return 'bg-red-500';\n        if (isNearLimit) return 'bg-yellow-500';\n        return 'bg-green-500';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between text-sm \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            label,\n                            \":\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    !isUnlimited && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 bg-gray-200 rounded-full h-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"h-1.5 rounded-full \".concat(getProgressBarColor()),\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"\".concat(Math.min(percentage, 100), \"%\")\n                            },\n                            transition: {\n                                duration: 0.5,\n                                ease: \"easeOut\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-medium \".concat(getStatusColor()),\n                        children: isUnlimited ? 'Unlimited' : \"\".concat(current, \"/\").concat(limit)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    (isAtLimit || isNearLimit) && showUpgradeHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-xs text-orange-600 hover:text-orange-700 underline ml-2\",\n                        onClick: ()=>{\n                            // You can add upgrade logic here\n                            console.log('Upgrade clicked');\n                        },\n                        children: \"Upgrade\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_c = LimitIndicator;\nvar _c;\n$RefreshReg$(_c, \"LimitIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RpZXJFbmZvcmNlbWVudC9MaW1pdEluZGljYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2E7QUFDNkQ7QUFhN0YsU0FBU0ssZUFBZSxLQU9UO1FBUFMsRUFDN0JDLE9BQU8sRUFDUEMsS0FBSyxFQUNMQyxLQUFLLEVBQ0xDLElBQUksRUFDSkMsa0JBQWtCLElBQUksRUFDdEJDLFlBQVksRUFBRSxFQUNNLEdBUFM7SUFRN0IsTUFBTUMsY0FBY0wsU0FBUztJQUM3QixNQUFNTSxhQUFhRCxjQUFjLElBQUksVUFBV0wsUUFBUztJQUN6RCxNQUFNTyxjQUFjRCxjQUFjO0lBQ2xDLE1BQU1FLFlBQVlULFdBQVdDLFNBQVMsQ0FBQ0s7SUFFdkMsTUFBTUksaUJBQWlCO1FBQ3JCLElBQUlKLGFBQWEsT0FBTztRQUN4QixJQUFJRyxXQUFXLE9BQU87UUFDdEIsSUFBSUQsYUFBYSxPQUFPO1FBQ3hCLE9BQU87SUFDVDtJQUVBLE1BQU1HLGdCQUFnQjtRQUNwQixJQUFJTCxhQUFhLHFCQUFPLDhEQUFDVCw2SUFBZUE7WUFBQ1EsV0FBVTs7Ozs7O1FBQ25ELElBQUlJLFdBQVcscUJBQU8sOERBQUNYLDZJQUFXQTtZQUFDTyxXQUFVOzs7Ozs7UUFDN0MsSUFBSUcsYUFBYSxxQkFBTyw4REFBQ1osNklBQXVCQTtZQUFDUyxXQUFVOzs7Ozs7UUFDM0QscUJBQU8sOERBQUNSLDZJQUFlQTtZQUFDUSxXQUFVOzs7Ozs7SUFDcEM7SUFFQSxNQUFNTyxzQkFBc0I7UUFDMUIsSUFBSU4sYUFBYSxPQUFPO1FBQ3hCLElBQUlHLFdBQVcsT0FBTztRQUN0QixJQUFJRCxhQUFhLE9BQU87UUFDeEIsT0FBTztJQUNUO0lBRUEscUJBQ0UsOERBQUNLO1FBQUlSLFdBQVcsNkNBQXVELE9BQVZBOzswQkFDM0QsOERBQUNRO2dCQUFJUixXQUFVOztrQ0FDYiw4REFBQ1M7d0JBQUtULFdBQVU7OzRCQUFpQkg7NEJBQU07Ozs7Ozs7b0JBQ3RDLENBQUNJLDZCQUNBLDhEQUFDTzt3QkFBSVIsV0FBVTtrQ0FDYiw0RUFBQ1YsaURBQU1BLENBQUNrQixHQUFHOzRCQUNUUixXQUFXLHNCQUE0QyxPQUF0Qk87NEJBQ2pDRyxTQUFTO2dDQUFFQyxPQUFPOzRCQUFFOzRCQUNwQkMsU0FBUztnQ0FBRUQsT0FBTyxHQUE2QixPQUExQkUsS0FBS0MsR0FBRyxDQUFDWixZQUFZLE1BQUs7NEJBQUc7NEJBQ2xEYSxZQUFZO2dDQUFFQyxVQUFVO2dDQUFLQyxNQUFNOzRCQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLckQsOERBQUNUO2dCQUFJUixXQUFVOztrQ0FDYiw4REFBQ1M7d0JBQUtULFdBQVcsdUJBQXdDLE9BQWpCSztrQ0FDckNKLGNBQWMsY0FBYyxHQUFjTCxPQUFYRCxTQUFRLEtBQVMsT0FBTkM7Ozs7OztvQkFFM0NRLENBQUFBLGFBQWFELFdBQVUsS0FBTUosaUNBQzdCLDhEQUFDbUI7d0JBQ0NsQixXQUFVO3dCQUNWbUIsU0FBUzs0QkFDUCxpQ0FBaUM7NEJBQ2pDQyxRQUFRQyxHQUFHLENBQUM7d0JBQ2Q7a0NBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9YO0tBbkVnQjNCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcY29tcG9uZW50c1xcVGllckVuZm9yY2VtZW50XFxMaW1pdEluZGljYXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uIH0gZnJvbSAnZnJhbWVyLW1vdGlvbic7XG5pbXBvcnQgeyBFeGNsYW1hdGlvblRyaWFuZ2xlSWNvbiwgQ2hlY2tDaXJjbGVJY29uLCBYQ2lyY2xlSWNvbiB9IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5pbXBvcnQgeyBTdWJzY3JpcHRpb25UaWVyIH0gZnJvbSAnQC9saWIvc3RyaXBlLWNsaWVudCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuXG5pbnRlcmZhY2UgTGltaXRJbmRpY2F0b3JQcm9wcyB7XG4gIGN1cnJlbnQ6IG51bWJlcjtcbiAgbGltaXQ6IG51bWJlcjtcbiAgbGFiZWw6IHN0cmluZztcbiAgdGllcjogU3Vic2NyaXB0aW9uVGllcjtcbiAgc2hvd1VwZ3JhZGVIaW50PzogYm9vbGVhbjtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gTGltaXRJbmRpY2F0b3IoeyBcbiAgY3VycmVudCwgXG4gIGxpbWl0LCBcbiAgbGFiZWwsIFxuICB0aWVyLFxuICBzaG93VXBncmFkZUhpbnQgPSB0cnVlLFxuICBjbGFzc05hbWUgPSAnJyBcbn06IExpbWl0SW5kaWNhdG9yUHJvcHMpIHtcbiAgY29uc3QgaXNVbmxpbWl0ZWQgPSBsaW1pdCA+PSA5OTk5OTk7XG4gIGNvbnN0IHBlcmNlbnRhZ2UgPSBpc1VubGltaXRlZCA/IDAgOiAoY3VycmVudCAvIGxpbWl0KSAqIDEwMDtcbiAgY29uc3QgaXNOZWFyTGltaXQgPSBwZXJjZW50YWdlID49IDgwO1xuICBjb25zdCBpc0F0TGltaXQgPSBjdXJyZW50ID49IGxpbWl0ICYmICFpc1VubGltaXRlZDtcblxuICBjb25zdCBnZXRTdGF0dXNDb2xvciA9ICgpID0+IHtcbiAgICBpZiAoaXNVbmxpbWl0ZWQpIHJldHVybiAndGV4dC1ncmVlbi02MDAnO1xuICAgIGlmIChpc0F0TGltaXQpIHJldHVybiAndGV4dC1yZWQtNjAwJztcbiAgICBpZiAoaXNOZWFyTGltaXQpIHJldHVybiAndGV4dC15ZWxsb3ctNjAwJztcbiAgICByZXR1cm4gJ3RleHQtZ3JlZW4tNjAwJztcbiAgfTtcblxuICBjb25zdCBnZXRTdGF0dXNJY29uID0gKCkgPT4ge1xuICAgIGlmIChpc1VubGltaXRlZCkgcmV0dXJuIDxDaGVja0NpcmNsZUljb24gY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LWdyZWVuLTYwMFwiIC8+O1xuICAgIGlmIChpc0F0TGltaXQpIHJldHVybiA8WENpcmNsZUljb24gY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXJlZC02MDBcIiAvPjtcbiAgICBpZiAoaXNOZWFyTGltaXQpIHJldHVybiA8RXhjbGFtYXRpb25UcmlhbmdsZUljb24gY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXllbGxvdy02MDBcIiAvPjtcbiAgICByZXR1cm4gPENoZWNrQ2lyY2xlSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtZ3JlZW4tNjAwXCIgLz47XG4gIH07XG5cbiAgY29uc3QgZ2V0UHJvZ3Jlc3NCYXJDb2xvciA9ICgpID0+IHtcbiAgICBpZiAoaXNVbmxpbWl0ZWQpIHJldHVybiAnYmctZ3JlZW4tNTAwJztcbiAgICBpZiAoaXNBdExpbWl0KSByZXR1cm4gJ2JnLXJlZC01MDAnO1xuICAgIGlmIChpc05lYXJMaW1pdCkgcmV0dXJuICdiZy15ZWxsb3ctNTAwJztcbiAgICByZXR1cm4gJ2JnLWdyZWVuLTUwMCc7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPntsYWJlbH06PC9zcGFuPlxuICAgICAgICB7IWlzVW5saW1pdGVkICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTYgYmctZ3JheS0yMDAgcm91bmRlZC1mdWxsIGgtMS41XCI+XG4gICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLTEuNSByb3VuZGVkLWZ1bGwgJHtnZXRQcm9ncmVzc0JhckNvbG9yKCl9YH1cbiAgICAgICAgICAgICAgaW5pdGlhbD17eyB3aWR0aDogMCB9fVxuICAgICAgICAgICAgICBhbmltYXRlPXt7IHdpZHRoOiBgJHtNYXRoLm1pbihwZXJjZW50YWdlLCAxMDApfSVgIH19XG4gICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZWFzZTogXCJlYXNlT3V0XCIgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCI+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRTdGF0dXNDb2xvcigpfWB9PlxuICAgICAgICAgIHtpc1VubGltaXRlZCA/ICdVbmxpbWl0ZWQnIDogYCR7Y3VycmVudH0vJHtsaW1pdH1gfVxuICAgICAgICA8L3NwYW4+XG4gICAgICAgIHsoaXNBdExpbWl0IHx8IGlzTmVhckxpbWl0KSAmJiBzaG93VXBncmFkZUhpbnQgJiYgKFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1vcmFuZ2UtNjAwIGhvdmVyOnRleHQtb3JhbmdlLTcwMCB1bmRlcmxpbmUgbWwtMlwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIC8vIFlvdSBjYW4gYWRkIHVwZ3JhZGUgbG9naWMgaGVyZVxuICAgICAgICAgICAgICBjb25zb2xlLmxvZygnVXBncmFkZSBjbGlja2VkJyk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIFVwZ3JhZGVcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwibW90aW9uIiwiRXhjbGFtYXRpb25UcmlhbmdsZUljb24iLCJDaGVja0NpcmNsZUljb24iLCJYQ2lyY2xlSWNvbiIsIkxpbWl0SW5kaWNhdG9yIiwiY3VycmVudCIsImxpbWl0IiwibGFiZWwiLCJ0aWVyIiwic2hvd1VwZ3JhZGVIaW50IiwiY2xhc3NOYW1lIiwiaXNVbmxpbWl0ZWQiLCJwZXJjZW50YWdlIiwiaXNOZWFyTGltaXQiLCJpc0F0TGltaXQiLCJnZXRTdGF0dXNDb2xvciIsImdldFN0YXR1c0ljb24iLCJnZXRQcm9ncmVzc0JhckNvbG9yIiwiZGl2Iiwic3BhbiIsImluaXRpYWwiLCJ3aWR0aCIsImFuaW1hdGUiLCJNYXRoIiwibWluIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiZWFzZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjb25zb2xlIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/LimitIndicator.tsx\n"));

/***/ })

});