"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/my-models/page",{

/***/ "(app-pages-browser)/./src/components/TierEnforcement/LimitIndicator.tsx":
/*!***********************************************************!*\
  !*** ./src/components/TierEnforcement/LimitIndicator.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LimitIndicator: () => (/* binding */ LimitIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,XCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ LimitIndicator auto */ \n\n\n\nfunction LimitIndicator(param) {\n    let { current, limit, label, tier, showUpgradeHint = true, className = '' } = param;\n    const isUnlimited = limit >= 999999;\n    const percentage = isUnlimited ? 0 : current / limit * 100;\n    const isNearLimit = percentage >= 80;\n    const isAtLimit = current >= limit && !isUnlimited;\n    const getStatusColor = ()=>{\n        if (isUnlimited) return 'text-green-600';\n        if (isAtLimit) return 'text-red-600';\n        if (isNearLimit) return 'text-yellow-600';\n        return 'text-green-600';\n    };\n    const getStatusIcon = ()=>{\n        if (isUnlimited) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 38,\n            columnNumber: 29\n        }, this);\n        if (isAtLimit) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-5 h-5 text-red-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 39,\n            columnNumber: 27\n        }, this);\n        if (isNearLimit) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"w-5 h-5 text-yellow-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 40,\n            columnNumber: 29\n        }, this);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_XCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            className: \"w-5 h-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n            lineNumber: 41,\n            columnNumber: 12\n        }, this);\n    };\n    const getProgressBarColor = ()=>{\n        if (isUnlimited) return 'bg-green-500';\n        if (isAtLimit) return 'bg-red-500';\n        if (isNearLimit) return 'bg-yellow-500';\n        return 'bg-green-500';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between text-sm \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            label,\n                            \":\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    !isUnlimited && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 bg-gray-200 rounded-full h-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            className: \"h-1.5 rounded-full \".concat(getProgressBarColor()),\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"\".concat(Math.min(percentage, 100), \"%\")\n                            },\n                            transition: {\n                                duration: 0.5,\n                                ease: \"easeOut\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-medium \".concat(getStatusColor()),\n                        children: isUnlimited ? 'Unlimited' : \"\".concat(current, \"/\").concat(limit)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    (isAtLimit || isNearLimit) && showUpgradeHint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-xs text-orange-600 hover:text-orange-700 underline ml-2\",\n                        onClick: ()=>{\n                            // You can add upgrade logic here\n                            console.log('Upgrade clicked');\n                        },\n                        children: \"Upgrade\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TierEnforcement\\\\LimitIndicator.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, this);\n}\n_c = LimitIndicator;\nvar _c;\n$RefreshReg$(_c, \"LimitIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TierEnforcement/LimitIndicator.tsx\n"));

/***/ })

});